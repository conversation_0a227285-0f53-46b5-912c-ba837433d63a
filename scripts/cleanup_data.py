#!/usr/bin/env python3
"""
Production-grade data cleanup script.
Cleans both Django database and Qdrant vector database safely.
"""

import os
import sys
import django
import requests
from typing import List

# Setup Django
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)
os.chdir(project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.conf import settings
from apps.documents.models import RawDocument, DocumentChunk, DocumentSource, EmbeddingMetadata
from apps.search.models import SearchResult, SearchQuery, ResultCitation
from apps.accounts.models import Tenant

def cleanup_qdrant_collections():
    """Clean all Qdrant collections safely."""
    print("🗑️  CLEANING QDRANT VECTOR DATABASE")
    print("-" * 40)
    
    qdrant_host = getattr(settings, 'QDRANT_HOST', 'localhost')
    qdrant_port = getattr(settings, 'QDRANT_PORT', 6333)
    qdrant_url = f"http://{qdrant_host}:{qdrant_port}"
    
    try:
        # Get all collections
        response = requests.get(f"{qdrant_url}/collections")
        if response.status_code == 200:
            collections = response.json()
            collection_names = [col['name'] for col in collections.get('result', {}).get('collections', [])]
            
            print(f"Found {len(collection_names)} collections:")
            for name in collection_names:
                print(f"   - {name}")
            
            # Delete each collection
            deleted_count = 0
            for collection_name in collection_names:
                try:
                    delete_response = requests.delete(f"{qdrant_url}/collections/{collection_name}")
                    if delete_response.status_code in [200, 404]:
                        print(f"   ✅ Deleted collection: {collection_name}")
                        deleted_count += 1
                    else:
                        print(f"   ❌ Failed to delete {collection_name}: {delete_response.status_code}")
                except Exception as e:
                    print(f"   ❌ Error deleting {collection_name}: {e}")
            
            print(f"✅ Deleted {deleted_count}/{len(collection_names)} collections")
            return True
            
        else:
            print(f"❌ Failed to get collections: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Qdrant. Is it running on localhost:6333?")
        return False
    except Exception as e:
        print(f"❌ Error cleaning Qdrant: {e}")
        return False

def cleanup_django_database():
    """Clean Django database in proper order."""
    print("\n🗑️  CLEANING DJANGO DATABASE")
    print("-" * 40)
    
    # Get counts before deletion
    counts = {
        'SearchResults': SearchResult.objects.count(),
        'Citations': ResultCitation.objects.count(),
        'SearchQueries': SearchQuery.objects.count(),
        'EmbeddingMetadata': EmbeddingMetadata.objects.count(),
        'DocumentChunks': DocumentChunk.objects.count(),
        'RawDocuments': RawDocument.objects.count(),
        'DocumentSources': DocumentSource.objects.count(),
    }
    
    print(f"Before cleanup:")
    for model, count in counts.items():
        print(f"   {model}: {count:,}")
    
    try:
        # Delete in proper order to avoid foreign key constraints
        print(f"\nDeleting data...")
        
        # 1. Delete search-related data
        deleted = ResultCitation.objects.all().delete()
        print(f"   ✅ Deleted {deleted[0]:,} citations")
        
        deleted = SearchResult.objects.all().delete()
        print(f"   ✅ Deleted {deleted[0]:,} search results")
        
        deleted = SearchQuery.objects.all().delete()
        print(f"   ✅ Deleted {deleted[0]:,} search queries")
        
        # 2. Delete embedding metadata
        deleted = EmbeddingMetadata.objects.all().delete()
        print(f"   ✅ Deleted {deleted[0]:,} embedding metadata")
        
        # 3. Delete document chunks
        deleted = DocumentChunk.objects.all().delete()
        print(f"   ✅ Deleted {deleted[0]:,} document chunks")
        
        # 4. Delete documents
        deleted = RawDocument.objects.all().delete()
        print(f"   ✅ Deleted {deleted[0]:,} documents")
        
        # Note: Keep DocumentSources as they represent configurations
        
        print(f"✅ Django database cleaned successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning Django database: {e}")
        return False

def verify_cleanup():
    """Verify cleanup was successful."""
    print("\n🔍 VERIFYING CLEANUP")
    print("-" * 40)
    
    # Check Django database
    remaining_counts = {
        'SearchResults': SearchResult.objects.count(),
        'Citations': ResultCitation.objects.count(),
        'SearchQueries': SearchQuery.objects.count(),
        'EmbeddingMetadata': EmbeddingMetadata.objects.count(),
        'DocumentChunks': DocumentChunk.objects.count(),
        'RawDocuments': RawDocument.objects.count(),
        'DocumentSources': DocumentSource.objects.count(),  # Should remain
    }
    
    print(f"Django database remaining:")
    all_clean = True
    for model, count in remaining_counts.items():
        if model == 'DocumentSources':
            status = "✅" if count > 0 else "⚠️"  # Sources should remain
            print(f"   {status} {model}: {count:,} (preserved)")
        else:
            status = "✅" if count == 0 else "❌"
            if count > 0:
                all_clean = False
            print(f"   {status} {model}: {count:,}")
    
    # Check Qdrant
    try:
        qdrant_host = getattr(settings, 'QDRANT_HOST', 'localhost')
        qdrant_port = getattr(settings, 'QDRANT_PORT', 6333)
        qdrant_url = f"http://{qdrant_host}:{qdrant_port}"
        
        response = requests.get(f"{qdrant_url}/collections")
        if response.status_code == 200:
            collections = response.json()
            collection_count = len(collections.get('result', {}).get('collections', []))
            status = "✅" if collection_count == 0 else "❌"
            if collection_count > 0:
                all_clean = False
            print(f"   {status} Qdrant collections: {collection_count}")
        else:
            print(f"   ❌ Cannot verify Qdrant collections")
            all_clean = False
    except:
        print(f"   ❌ Cannot connect to Qdrant for verification")
        all_clean = False
    
    return all_clean

def main():
    """Main cleanup function."""
    print("🧹 PRODUCTION-GRADE DATA CLEANUP")
    print("=" * 60)
    print("⚠️  WARNING: This will delete ALL data!")
    print("   - All documents and chunks")
    print("   - All search results and queries")
    print("   - All embeddings and vector data")
    print("   - All Qdrant collections")
    print("   - DocumentSources will be preserved")
    print()
    
    # Get confirmation
    confirmation = input("Are you sure you want to proceed? Type 'YES' to confirm: ")
    
    if confirmation != 'YES':
        print("❌ Cleanup cancelled")
        return False
    
    print("\n🚀 Starting cleanup...")
    
    # Clean Qdrant first (safer order)
    qdrant_success = cleanup_qdrant_collections()
    
    # Clean Django database
    django_success = cleanup_django_database()
    
    # Verify cleanup
    verification_success = verify_cleanup()
    
    # Final status
    print(f"\n🎯 CLEANUP SUMMARY")
    print("=" * 60)
    print(f"   Qdrant cleanup: {'✅ Success' if qdrant_success else '❌ Failed'}")
    print(f"   Django cleanup: {'✅ Success' if django_success else '❌ Failed'}")
    print(f"   Verification: {'✅ Success' if verification_success else '❌ Failed'}")
    
    overall_success = qdrant_success and django_success and verification_success
    
    if overall_success:
        print(f"\n🎉 CLEANUP COMPLETE!")
        print("Ready for fresh data ingestion.")
    else:
        print(f"\n⚠️ CLEANUP INCOMPLETE!")
        print("Some operations failed. Check logs above.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
