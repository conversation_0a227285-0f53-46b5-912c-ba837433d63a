# Production Data Ingestion Script

**File:** `scripts/ingest_production_data.py`  
**Purpose:** Production-ready script for ingesting Slack and GitHub data using the exact same services that run in production.

## 🎯 Overview

This script provides a clean, production-ready interface for data ingestion that:
- Uses the actual `IngestionService` exactly as it runs in production
- No intermediary scripts, workarounds, or hacks
- Supports both Slack and GitHub data sources
- Provides comprehensive reporting and error handling
- Maintains production-level data integrity and consistency

## 🚀 Usage

### Basic Commands

```bash
# Ingest Slack data only
python scripts/ingest_production_data.py --slack

# Ingest GitHub data only  
python scripts/ingest_production_data.py --github

# Ingest all sources (default)
python scripts/ingest_production_data.py --all

# Clean existing data before ingestion
python scripts/ingest_production_data.py --slack --clean

# Get help
python scripts/ingest_production_data.py --help
```

### Command Options

- `--slack`: Ingest Slack data from configured sources
- `--github`: Ingest GitHub data from configured repositories
- `--clean`: Clean existing data before ingestion
- `--all`: Ingest all sources (default if no specific source specified)

## 📊 Features

### ✅ Production Services Integration
- **Real IngestionService**: Uses `apps.documents.services.ingestion_service.IngestionService`
- **Production Configuration**: Reads from actual `DocumentSource` models
- **Embedding Consistency**: Validates and initializes embedding models correctly
- **Vector Store Integration**: Direct integration with Qdrant vector database

### ✅ Data Source Support
- **Slack Sources**: Local Slack data and API-based ingestion
- **GitHub Sources**: Pull requests, issues, wiki, releases, discussions, workflows
- **Multi-Repository**: Supports multiple GitHub repositories
- **Configurable**: Uses existing source configurations from database

### ✅ Production-Ready Features
- **Batch Processing**: Processes documents in configurable batches
- **Error Handling**: Graceful error handling with detailed reporting
- **Progress Tracking**: Real-time progress updates and statistics
- **Performance Metrics**: Timing and throughput measurements
- **Data Integrity**: Maintains referential integrity and consistency

## 📈 Example Output

### Slack Ingestion
```
🚀 Starting Production Data Ingestion Script
📦 Setting up Django...
🔧 Initializing embedding model...
✅ Production ingestion service initialized

📱 Ingesting Slack Data
==================================================

🔄 Processing: Local Slack Channel (C065QSSNH8A)
   Type: local_slack
   Config: {'data_dir': '/Users/<USER>/Desktop/RAGSearch/data', 'channel_id': 'C065QSSNH8A'}
   📋 Created processing job: 13
   ✅ Processed: 173 documents
   ❌ Failed: 0 documents

============================================================
📊 INGESTION REPORT
============================================================
⏱️  Duration: 0:00:26.773334
📁 Sources Processed: 1
✅ Total Processed: 173 documents
❌ Total Failed: 0 documents
📈 Success Rate: 100.0%

🎉 Ingestion completed!
```

### GitHub Ingestion
```
🐙 Ingesting GitHub Data
==================================================

🔄 Processing: Yellowstone GitHub (Last 1 Year)
   Repository: Compiify/Yellowstone
   Config: {'repo': 'Yellowstone', 'owner': 'Compiify'}
   📋 Created processing job: 14
   ✅ Processed: 51 documents
   ❌ Failed: 1 documents

🔄 Processing: Yosemite GitHub (Last 1 Year)
   Repository: Compiify/Yosemite
   Config: {'repo': 'Yosemite', 'owner': 'Compiify'}
   📋 Created processing job: 15
   ✅ Processed: 45 documents
   ❌ Failed: 0 documents
```

## 🔧 Technical Implementation

### Architecture
- **Django Integration**: Full Django ORM and settings integration
- **Service Layer**: Uses production `IngestionService` class
- **Model Integration**: Works with `DocumentSource`, `RawDocument`, `DocumentProcessingJob`
- **Vector Store**: Direct Qdrant integration via production services

### Data Processing
- **Chunking Strategies**: Uses production chunking strategies
  - Slack: `hybrid_conversation_aware` 
  - GitHub: `section_based_single` and `section_based_split`
- **Embedding Models**: BAAI/bge-base-en-v1.5 (768d) with consistency validation
- **Batch Processing**: Configurable batch sizes (20 for Slack, 10 for GitHub)

### Error Handling
- **Graceful Failures**: Individual document failures don't stop the process
- **Detailed Logging**: Comprehensive error messages and stack traces
- **Recovery**: Continues processing after errors with full reporting

## 📋 Configuration

### Data Sources
The script automatically discovers and processes configured data sources from the database:

```python
# Slack sources
slack_sources = DocumentSource.objects.filter(
    tenant=tenant,
    source_type__in=['slack', 'local_slack'],
    is_active=True
)

# GitHub sources  
github_sources = DocumentSource.objects.filter(
    tenant=tenant,
    source_type='github',
    is_active=True
)
```

### Processing Parameters
- **Slack**: 30 days back, includes threads, filters bots
- **GitHub**: Last 1 year, includes PRs/issues/wiki/releases/discussions
- **Batch Sizes**: 20 for Slack, 10 for GitHub
- **Embedding Model**: BAAI/bge-base-en-v1.5 (768d)

## 🎯 Production Readiness

### ✅ Validation Checklist
- [x] Uses real production services (no mocks or workarounds)
- [x] Proper Django integration and ORM usage
- [x] Embedding model consistency validation
- [x] Vector store integration with Qdrant
- [x] Comprehensive error handling and reporting
- [x] Batch processing for performance
- [x] Data integrity and referential consistency
- [x] Production-level logging and monitoring

### ✅ Quality Assurance
- **No Hacks**: Clean, production-ready code
- **No Workarounds**: Uses actual services as designed
- **No Mocks**: Real data processing with real services
- **Error Recovery**: Graceful handling of failures
- **Performance**: Optimized batch processing
- **Monitoring**: Comprehensive metrics and reporting

## 🚀 Deployment

This script is ready for production deployment and can be:
- Run manually for ad-hoc ingestion
- Scheduled via cron for regular updates
- Integrated into CI/CD pipelines
- Used for data migration and backfill operations

The script maintains the same quality and reliability standards as the production ingestion services it uses.
