#!/usr/bin/env python3
"""
Production Readiness Check Script

This script performs final validation checks to ensure the RAG system
is ready for production deployment.
"""

import os
import sys
import django
import time
from datetime import datetime
from typing import Dict, List, Any

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

# Load environment variables first
from dotenv import load_dotenv
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag', '.env'))

# Override Django settings to use production for this check
os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings.production'
django.setup()

from apps.search.services.rag_service import RAGService
from apps.documents.services.ingestion_service import IngestionService
from apps.accounts.models import Tenant, User
from apps.documents.models import DocumentSource, RawDocument
from apps.core.utils.chunking_strategies import get_chunking_strategy_info


class ProductionReadinessChecker:
    """Final production readiness validation."""
    
    def __init__(self):
        self.checks = {
            "critical": [],
            "warnings": [],
            "passed": []
        }
        self.start_time = datetime.now()
    
    def add_check(self, level: str, message: str):
        """Add a check result."""
        self.checks[level].append(message)
        if level == "critical":
            print(f"🚨 CRITICAL: {message}")
        elif level == "warnings":
            print(f"⚠️  WARNING: {message}")
        else:
            print(f"✅ PASSED: {message}")
    
    def check_system_dependencies(self):
        """Check system dependencies and services."""
        print("🔍 Checking System Dependencies...")
        
        # Check Qdrant connection
        try:
            import qdrant_client
            client = qdrant_client.QdrantClient(host="localhost", port=6333)
            collections = client.get_collections()
            self.add_check("passed", f"Qdrant connected with {len(collections.collections)} collections")
        except Exception as e:
            self.add_check("critical", f"Qdrant connection failed: {str(e)}")
        
        # Check Django database
        try:
            tenant_count = Tenant.objects.count()
            user_count = User.objects.count()
            self.add_check("passed", f"Database connected: {tenant_count} tenants, {user_count} users")
        except Exception as e:
            self.add_check("critical", f"Database connection failed: {str(e)}")
    
    def check_data_integrity(self):
        """Check data integrity and consistency."""
        print("\n🔍 Checking Data Integrity...")
        
        try:
            # Check document counts
            doc_count = RawDocument.objects.count()
            source_count = DocumentSource.objects.count()
            
            if doc_count == 0:
                self.add_check("warnings", "No documents found - ingestion may be needed")
            else:
                self.add_check("passed", f"Found {doc_count} documents from {source_count} sources")
            
            # Check for orphaned documents
            orphaned = RawDocument.objects.filter(source__isnull=True).count()
            if orphaned > 0:
                self.add_check("warnings", f"Found {orphaned} orphaned documents")
            else:
                self.add_check("passed", "No orphaned documents found")
                
        except Exception as e:
            self.add_check("critical", f"Data integrity check failed: {str(e)}")
    
    def check_rag_functionality(self):
        """Check RAG service functionality."""
        print("\n🔍 Checking RAG Functionality...")
        
        try:
            # Get test data
            tenant = Tenant.objects.first()
            user = User.objects.first()
            
            if not tenant or not user:
                self.add_check("critical", "No tenant or user found for RAG testing")
                return
            
            # Initialize RAG service
            rag_service = RAGService(user=user, tenant_slug=tenant.slug)
            self.add_check("passed", "RAG service initialized successfully")
            
            # Test search
            start_time = time.time()
            search_result, documents = rag_service.search("test query", top_k=3)
            search_time = time.time() - start_time
            
            if search_time > 30:
                self.add_check("warnings", f"Search took {search_time:.1f}s (>30s threshold)")
            else:
                self.add_check("passed", f"Search completed in {search_time:.1f}s")
            
            if len(documents) == 0:
                self.add_check("warnings", "Search returned no documents")
            else:
                self.add_check("passed", f"Search returned {len(documents)} documents")
                
        except Exception as e:
            self.add_check("critical", f"RAG functionality check failed: {str(e)}")
    
    def check_chunking_strategies(self):
        """Check chunking strategy configuration."""
        print("\n🔍 Checking Chunking Strategies...")
        
        try:
            source_types = [
                "slack", "local_slack", "github_pr", "github_issue",
                "confluence", "notion", "web", "pdf", "file"
            ]
            
            all_valid = True
            for source_type in source_types:
                try:
                    strategy_info = get_chunking_strategy_info(source_type)
                    required_fields = ["strategy", "skip_chunking", "description"]
                    
                    if all(field in strategy_info for field in required_fields):
                        self.add_check("passed", f"Chunking strategy valid for {source_type}")
                    else:
                        self.add_check("critical", f"Invalid chunking strategy for {source_type}")
                        all_valid = False
                        
                except Exception as e:
                    self.add_check("critical", f"Chunking strategy error for {source_type}: {str(e)}")
                    all_valid = False
            
            if all_valid:
                self.add_check("passed", "All chunking strategies properly configured")
                
        except Exception as e:
            self.add_check("critical", f"Chunking strategy check failed: {str(e)}")
    
    def check_embedding_consistency(self):
        """Check embedding model consistency."""
        print("\n🔍 Checking Embedding Consistency...")
        
        try:
            # Initialize ingestion service to trigger embedding validation
            tenant = Tenant.objects.first()
            user = User.objects.first()
            
            if not tenant or not user:
                self.add_check("warnings", "No tenant or user found for embedding check")
                return
            
            ingestion_service = IngestionService(tenant=tenant, user=user)
            self.add_check("passed", "Embedding consistency validated")
            
        except Exception as e:
            if "inconsistent" in str(e).lower():
                self.add_check("critical", f"Embedding inconsistency detected: {str(e)}")
            else:
                self.add_check("warnings", f"Embedding check warning: {str(e)}")
    
    def check_security_configuration(self):
        """Check security configuration."""
        print("\n🔍 Checking Security Configuration...")
        
        # Check Django settings
        from django.conf import settings
        
        if settings.DEBUG:
            self.add_check("warnings", "DEBUG mode is enabled - should be False in production")
        else:
            self.add_check("passed", "DEBUG mode properly disabled")
        
        if hasattr(settings, 'SECRET_KEY') and len(settings.SECRET_KEY) > 20:
            self.add_check("passed", "SECRET_KEY properly configured")
        else:
            self.add_check("critical", "SECRET_KEY not properly configured")
        
        # Check for required environment variables
        required_env_vars = ['GEMINI_API_KEY']
        for var in required_env_vars:
            if os.getenv(var):
                self.add_check("passed", f"Environment variable {var} configured")
            else:
                self.add_check("warnings", f"Environment variable {var} not set")
    
    def generate_final_report(self):
        """Generate final production readiness report."""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 80)
        print("🏭 PRODUCTION READINESS REPORT")
        print("=" * 80)
        
        print(f"⏱️  Check Duration: {duration.total_seconds():.2f} seconds")
        print(f"✅ Passed Checks: {len(self.checks['passed'])}")
        print(f"⚠️  Warnings: {len(self.checks['warnings'])}")
        print(f"🚨 Critical Issues: {len(self.checks['critical'])}")
        
        # Show critical issues
        if self.checks["critical"]:
            print("\n🚨 CRITICAL ISSUES (MUST FIX BEFORE PRODUCTION):")
            for issue in self.checks["critical"]:
                print(f"  • {issue}")
        
        # Show warnings
        if self.checks["warnings"]:
            print("\n⚠️  WARNINGS (RECOMMENDED TO FIX):")
            for warning in self.checks["warnings"]:
                print(f"  • {warning}")
        
        # Final assessment
        print("\n🏆 FINAL ASSESSMENT:")
        if len(self.checks["critical"]) == 0:
            if len(self.checks["warnings"]) == 0:
                print("✅ SYSTEM IS FULLY PRODUCTION READY")
                print("   All checks passed with no issues.")
            else:
                print("⚠️  SYSTEM IS PRODUCTION READY WITH MINOR WARNINGS")
                print("   Core functionality works but some optimizations recommended.")
        else:
            print("❌ SYSTEM NOT READY FOR PRODUCTION")
            print("   Critical issues must be resolved before deployment.")
        
        # Deployment checklist
        print("\n📋 PRE-DEPLOYMENT CHECKLIST:")
        print("  □ All critical issues resolved")
        print("  □ Environment variables configured")
        print("  □ Database migrations applied")
        print("  □ Static files collected")
        print("  □ SSL certificates configured")
        print("  □ Backup strategy implemented")
        print("  □ Monitoring and logging configured")
        print("  □ Load testing completed")
    
    def run_all_checks(self):
        """Run all production readiness checks."""
        print("🚀 Production Readiness Check")
        print("=" * 80)
        
        self.check_system_dependencies()
        self.check_data_integrity()
        self.check_rag_functionality()
        self.check_chunking_strategies()
        self.check_embedding_consistency()
        self.check_security_configuration()
        
        self.generate_final_report()


def main():
    """Main function to run production readiness checks."""
    checker = ProductionReadinessChecker()
    checker.run_all_checks()


if __name__ == "__main__":
    main()
