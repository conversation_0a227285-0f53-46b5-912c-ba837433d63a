#!/usr/bin/env python3
"""
Test script for GitHub integration.

This script tests the consolidated GitHub interface with real repositories.
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource
from apps.documents.services.ingestion_service import IngestionService
from apps.documents.interfaces.factory import DocumentSourceFactory

def test_github_interface():
    """Test the consolidated GitHub interface."""
    print("🔧 Testing Consolidated GitHub Interface")
    print("=" * 50)
    
    # GitHub configuration
    config = {
        "token": "****************************************",
        "owner": "Compiify",
        "repo": "Yellowstone",
        "content_types": ["pull_request", "issue", "wiki", "release", "workflow"]
    }
    
    try:
        # Initialize interface using factory
        github_interface = DocumentSourceFactory.create_source("github", config)
        print(f"✅ Successfully initialized GitHub interface for {config['owner']}/{config['repo']}")
        
        # Test configuration validation
        if github_interface.validate_config():
            print("✅ Configuration validation passed")
        else:
            print("❌ Configuration validation failed")
            return
        
        # Test fetching documents
        print("\n📥 Fetching documents...")
        documents = github_interface.fetch_documents(
            days=30,
            max_per_type=5  # Limit for testing
        )
        
        print(f"✅ Fetched {len(documents)} documents")
        
        # Display statistics
        stats = github_interface.get_stats()
        print(f"\n📊 Statistics:")
        print(f"   Total documents: {stats['total_documents']}")
        print(f"   Processing time: {stats['processing_time']:.2f}s")
        print(f"   By content type:")
        for content_type, count in stats['by_content_type'].items():
            print(f"     - {content_type}: {count}")
        
        # Display sample documents
        print(f"\n📄 Sample documents:")
        for i, doc in enumerate(documents[:3]):
            print(f"   {i+1}. {doc['title']} ({doc['metadata']['content_type']})")
            print(f"      ID: {doc['id']}")
            print(f"      Created: {doc.get('created_at', 'N/A')}")
            print(f"      Content preview: {doc['content'][:100]}...")
            print()
        
        return documents
        
    except Exception as e:
        print(f"❌ Error testing GitHub interface: {str(e)}")
        return None

def test_github_ingestion():
    """Test GitHub ingestion through the ingestion service."""
    print("\n🔄 Testing GitHub Ingestion Service")
    print("=" * 50)
    
    try:
        # Get tenant
        tenant = Tenant.objects.first()
        if not tenant:
            print("❌ No tenant found. Please create a tenant first.")
            return
        
        # Create or get GitHub source
        source, created = DocumentSource.objects.get_or_create(
            tenant=tenant,
            name="Yellowstone GitHub",
            source_type="github",
            defaults={
                "config": {
                    "token": "****************************************",
                    "owner": "Compiify",
                    "repo": "Yellowstone",
                    "content_types": ["pull_request", "issue", "release"]
                },
                "is_active": True
            }
        )
        
        if created:
            print(f"✅ Created new GitHub source: {source.name}")
        else:
            print(f"✅ Using existing GitHub source: {source.name}")
        
        # Create ingestion service
        ingestion_service = IngestionService(tenant)
        
        # Process source
        print("📥 Starting ingestion...")
        processed, failed = ingestion_service.process_source(
            source=source,
            batch_size=10,
            days=30,
            max_per_type=5
        )
        
        print(f"✅ Ingestion completed!")
        print(f"   Processed: {processed} documents")
        print(f"   Failed: {failed} documents")
        
        return processed, failed
        
    except Exception as e:
        print(f"❌ Error testing GitHub ingestion: {str(e)}")
        return None, None

def main():
    """Main test function."""
    print("🚀 GitHub Integration Test Suite")
    print("=" * 60)
    
    # Test 1: Interface functionality
    documents = test_github_interface()
    
    if documents:
        # Test 2: Ingestion service
        processed, failed = test_github_ingestion()
        
        if processed is not None:
            print(f"\n🎉 All tests completed successfully!")
            print(f"   Interface fetched: {len(documents)} documents")
            print(f"   Ingestion processed: {processed} documents")
        else:
            print(f"\n⚠️  Interface test passed, but ingestion test failed")
    else:
        print(f"\n❌ Interface test failed, skipping ingestion test")

if __name__ == "__main__":
    main()
