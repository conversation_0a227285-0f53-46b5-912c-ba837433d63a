"""
LlamaIndex vector store integration.

This module provides integration with LlamaIndex vector stores, including:
- Qdrant vector store integration
- Vector store management
- Document indexing and retrieval
"""

import logging
import uuid
from typing import Any, Dict, List, Optional, Tuple, Union

from django.conf import settings
from llama_index import Document
from llama_index.schema import NodeWithScore, TextNode
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from qdrant_client.http import models as qdrant_models

from apps.accounts.models import Tenant
from apps.core.models import VectorIndex
from apps.core.utils.collection_manager import (
    get_collection_name, is_collection_usable, get_qdrant_client,
    get_fallback_collections, create_collection
)
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
# Removed circular import - registry not needed for direct vector store creation

logger = logging.getLogger(__name__)


# Using get_qdrant_client from collection_manager.py


def initialize_vector_stores() -> None:
    """Initialize and register vector stores with the registry."""
    # This function is kept for compatibility but vector stores are now created directly
    logger.info("Vector stores are now created directly when needed")


def get_vector_store(
    collection_name: Optional[str] = None,
    tenant_slug: Optional[str] = None,
    check_existence: bool = True,
    intent: Optional[str] = None,
) -> QdrantVectorStore:
    """
    Get a vector store instance.

    Args:
        collection_name: Name of the collection to use
        tenant_slug: Tenant slug for tenant-specific collections
        check_existence: Whether to check if the collection exists
        intent: Query intent for intent-specific collections

    Returns:
        QdrantVectorStore: Vector store instance
    """
    # Determine collection name based on tenant, intent, and fallbacks
    if not collection_name:
        collection_name = get_collection_name(tenant_slug, intent, check_existence)

    # Log the collection being used
    logger.info(f"Using collection: {collection_name}")

    # Create vector store directly
    try:
        qdrant_client = get_qdrant_client()

        # Get the consistent embedding model
        from apps.core.utils.embedding_consistency import get_consistent_embedding_model
        embed_model = get_consistent_embedding_model()

        # Create vector store with consistent embedding model
        vector_store = QdrantVectorStore(
            client=qdrant_client,
            collection_name=collection_name,
            enable_hybrid=False,  # Disable hybrid search for now
            embed_model=embed_model,  # Use consistent embedding model
        )

        return vector_store
    except Exception as e:
        logger.error(f"Failed to create vector store: {str(e)}")
        raise ValueError(f"Failed to create vector store: {str(e)}")


def ensure_collection_exists(
    collection_name: str,
    dimension: int = 384,
) -> None:
    """
    Ensure that a collection exists in the vector store.

    Args:
        collection_name: Name of the collection
        dimension: Dimension of the vectors
    """
    # Use the collection_manager utility
    if not create_collection(collection_name, dimension):
        raise ValueError(f"Failed to create collection: {collection_name}")


def get_or_create_vector_index(
    tenant: Tenant,
    name: str = "Default Vector Index",
    collection_name: Optional[str] = None,
    dimension: int = 384,
) -> VectorIndex:
    """
    Get or create a vector index in the database.

    Args:
        tenant: Tenant to create index for
        name: Name of the index
        collection_name: Name of the collection
        dimension: Dimension of the vectors

    Returns:
        VectorIndex: Database model for the vector index
    """
    # Determine collection name
    if not collection_name:
        collection_name = get_collection_name(tenant.slug, check_existence=True)

    # Get or create vector index
    vector_index, created = VectorIndex.objects.get_or_create(
        tenant=tenant,
        name=name,
        defaults={
            "vector_db_type": "qdrant",
            "collection_name": collection_name,
            "dimension": dimension,
            "is_active": True,
        },
    )

    if created:
        logger.info(f"Created vector index: {vector_index.name}")

        # Ensure collection exists
        ensure_collection_exists(collection_name, dimension)

    return vector_index


def add_documents_to_index(
    documents: List[Dict[str, Any]],
    collection_name: str,
    tenant_slug: Optional[str] = None,
    embedding_model_name: Optional[str] = None,
) -> List[str]:
    """
    Add documents to the vector store.

    Args:
        documents: List of documents to add
        collection_name: Name of the collection
        tenant_slug: Tenant slug for tenant-specific collections
        embedding_model_name: Name of the embedding model to use

    Returns:
        List[str]: List of document IDs
    """
    # Get vector store
    vector_store = get_vector_store(collection_name, tenant_slug)

    # Convert documents to LlamaIndex format
    llama_docs = []
    doc_ids = []

    for doc in documents:
        # Generate a UUID for the document
        doc_id = str(uuid.uuid4())
        doc_ids.append(doc_id)

        # Extract content and metadata
        content = doc.get("content", "")
        metadata = doc.get("metadata", {})

        # Add document ID to metadata
        metadata["doc_id"] = doc_id

        # Create LlamaIndex document
        llama_doc = Document(
            text=content,
            metadata=metadata,
            id_=doc_id,
        )
        llama_docs.append(llama_doc)

    # Add documents to vector store
    try:
        # Get embedding model
        embed_model = get_embedding_model_for_content(model_name=embedding_model_name)

        # Add documents to vector store
        # Convert documents to nodes
        nodes = [TextNode(text=doc.text, metadata=doc.metadata) for doc in llama_docs]

        # Generate embeddings for nodes
        for node in nodes:
            node_embedding = embed_model.get_text_embedding(node.text)
            node.embedding = node_embedding

        # Add nodes to vector store
        vector_store.add(nodes)

        logger.info(f"Added {len(llama_docs)} documents to collection {collection_name}")
        return doc_ids
    except Exception as e:
        logger.error(f"Error adding documents to vector store: {str(e)}")
        raise


def search_vector_store(
    query: str,
    collection_name: Optional[str] = None,
    tenant_slug: Optional[str] = None,
    metadata_filter: Optional[Dict[str, Any]] = None,
    k: int = 5,
    embedding_model_name: Optional[str] = None,
    try_fallback_collections: bool = True,
    intent: Optional[str] = None,
    enforce_tenant_isolation: bool = True,
) -> List[Tuple[Document, float]]:
    """
    Search the vector store for documents similar to the query.

    Args:
        query: Query string
        collection_name: Name of the collection
        tenant_slug: Tenant slug for tenant-specific collections
        metadata_filter: Metadata filter to apply
        k: Number of results to return
        embedding_model_name: Name of the embedding model to use
        try_fallback_collections: Whether to try fallback collections if primary fails
        intent: Query intent for intent-specific collections
        enforce_tenant_isolation: Whether to enforce tenant isolation in fallbacks

    Returns:
        List[Tuple[Document, float]]: List of documents and their similarity scores
    """
    # Get embedding model
    embed_model = get_embedding_model_for_content(model_name=embedding_model_name)

    # Convert metadata filter to Qdrant format
    qdrant_filter = None
    if metadata_filter:
        qdrant_filter = _convert_metadata_filter(metadata_filter)

    # Get collection name if not provided
    if not collection_name:
        collection_name = get_collection_name(tenant_slug, intent, check_existence=True)

    # Check if collection is usable (exists and has data)
    if not is_collection_usable(collection_name):
        logger.warning(f"Collection {collection_name} is not usable (empty or doesn't exist)")
        # Auto-fall back if the collection isn't usable
        try_fallback_collections = True

    # Try searching with primary collection
    results = []

    try:
        # Get vector store
        vector_store = get_vector_store(collection_name, tenant_slug, intent=intent)
        collection_to_use = vector_store.collection_name

        # Embed query
        query_embedding = embed_model.get_text_embedding(query)

        # Search vector store
        results = vector_store.similarity_search_with_score(
            query=query,
            k=k,
            filter=qdrant_filter,
        )

        logger.info(f"Found {len(results)} results in collection {collection_to_use}")
    except Exception as e:
        logger.error(f"Error searching primary collection: {str(e)}")
        # Primary search failed

    # If primary search returned no results and fallbacks are enabled, try alternatives
    if not results and try_fallback_collections and tenant_slug:
        # Get fallback collections in priority order
        fallback_collections = get_fallback_collections(
            collection_name, tenant_slug, enforce_tenant_isolation)

        logger.info(f"Will try fallback collections: {fallback_collections}")

        # Try each fallback collection
        for fallback_name in fallback_collections:
            try:
                # Skip collections that aren't usable (don't exist or are empty)
                if not is_collection_usable(fallback_name):
                    logger.info(f"Skipping unusable fallback collection: {fallback_name}")
                    continue

                # Get vector store with fallback collection
                vector_store = get_vector_store(fallback_name, None, check_existence=False)

                # Search vector store
                fallback_results = vector_store.similarity_search_with_score(
                    query=query,
                    k=k,
                    filter=qdrant_filter,
                )

                if fallback_results:
                    logger.info(f"Found {len(fallback_results)} results in fallback collection {fallback_name}")
                    results = fallback_results
                    break

            except Exception as e:
                logger.error(f"Error searching fallback collection {fallback_name}: {str(e)}")
                continue

    return results


def _convert_metadata_filter(metadata_filter: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert metadata filter to Qdrant format.

    Args:
        metadata_filter: Metadata filter

    Returns:
        Dict[str, Any]: Qdrant filter
    """
    if not metadata_filter:
        return None

    filter_conditions = []

    for key, value in metadata_filter.items():
        if isinstance(value, list):
            # For lists, use 'any' condition
            filter_conditions.append(
                {
                    "key": f"metadata.{key}",
                    "match": {"any": value},
                }
            )
        elif isinstance(value, dict) and "range" in value:
            # For range queries
            range_params = {}
            for range_key, range_value in value["range"].items():
                if range_key in ["gt", "gte", "lt", "lte"]:
                    range_params[range_key] = range_value

            if range_params:
                filter_conditions.append(
                    {
                        "key": f"metadata.{key}",
                        "range": range_params,
                    }
                )
        else:
            # For exact matches
            filter_conditions.append(
                {
                    "key": f"metadata.{key}",
                    "match": {"value": value},
                }
            )

    if filter_conditions:
        return {"must": filter_conditions}

    return None
