# Setup & Installation Guide

Complete guide for setting up RAGSearch in development and production environments.

## 🚀 Quick Start

### Prerequisites
- **Python 3.10+**
- **PostgreSQL 14+** 
- **Docker** (for Qdrant)
- **Gemini API Key** ([Get from Google AI Studio](https://makersuite.google.com/app/apikey))

### 1. Environment Setup

```bash
# Clone repository
git clone <repository-url>
cd RAGSearch

# Install dependencies
poetry install

# Activate virtual environment
poetry shell
```

### 2. Database Setup

```bash
# Start Qdrant vector database
docker run -d -p 6333:6333 --name qdrant qdrant/qdrant

# Create PostgreSQL database
createdb multi_source_rag

# Optional: Create dedicated user
createuser -P ragsearch_user
```

### 3. Configuration

Create `multi_source_rag/.env`:

```bash
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
DJANGO_SETTINGS_MODULE=config.settings.development

# Database Configuration
DB_NAME=multi_source_rag
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333

# AI Models
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-1.5-flash
GEMINI_EMBEDDING_MODEL=embedding-001

# Ollama (Optional - for local LLM)
OLLAMA_API_HOST=http://localhost:11434
OLLAMA_MODEL_NAME=llama3

# Data Sources (Optional)
SLACK_API_TOKEN=your-slack-token
GITHUB_TOKEN=your-github-token
```

### 4. Initialize Application

```bash
cd multi_source_rag

# Run database migrations
python manage.py migrate

# Create superuser account
python manage.py createsuperuser

# Collect static files (production)
python manage.py collectstatic --noinput

# Start development server
python manage.py runserver
```

### 5. Verify Installation

1. **Web Interface**: Visit `http://localhost:8000/search/`
2. **Admin Panel**: Visit `http://localhost:8000/admin/`
3. **API Health**: Visit `http://localhost:8000/api/health/`

## 🔧 Production Setup

### Docker Deployment

```bash
# Build and start services
docker-compose -f docker/docker-compose.yml up -d

# Initialize application
./docker/scripts/init.sh

# Check service status
docker-compose ps
```

### Environment Variables (Production)

```bash
# Security
SECRET_KEY=your-production-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database (Production)
DB_HOST=your-db-host
DB_NAME=ragsearch_prod
DB_USER=ragsearch_prod_user
DB_PASSWORD=secure-password

# SSL/HTTPS
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https

# Logging
LOG_LEVEL=INFO
```

## 🔍 Data Pipeline Setup

### 1. Clean Start (Recommended)

```bash
# Run complete data pipeline
python ../scripts/data_pipeline.py
```

### 2. Manual Steps

```bash
# Clean existing data
python ../scripts/cleanup_data.py

# Ingest fresh data
python ../scripts/ingest_data.py

# Validate data consistency
python ../scripts/validate_data_consistency.py
```

## 🛠️ Troubleshooting

### Common Issues

**1. Qdrant Connection Failed**
```bash
# Check if Qdrant is running
docker ps | grep qdrant

# Start Qdrant if not running
docker run -d -p 6333:6333 --name qdrant qdrant/qdrant

# Test connection
curl http://localhost:6333/cluster
```

**2. PostgreSQL Connection Issues**
```bash
# Check PostgreSQL status
pg_ctl status

# Test connection
psql -h localhost -U your_user -d multi_source_rag
```

**3. Gemini API Issues**
```bash
# Test API key
curl -H "Content-Type: application/json" \
     -d '{"contents":[{"parts":[{"text":"Hello"}]}]}' \
     "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=YOUR_API_KEY"
```

**4. Migration Errors**
```bash
# Reset migrations (development only)
python manage.py migrate --fake-initial

# Or start fresh
python manage.py flush
python manage.py migrate
```

### Performance Optimization

**1. Database Indexing**
```sql
-- Add custom indexes for better performance
CREATE INDEX CONCURRENTLY idx_document_chunks_tenant_id ON documents_documentchunk(tenant_id);
CREATE INDEX CONCURRENTLY idx_embedding_metadata_vector_id ON documents_embeddingmetadata(vector_id);
```

**2. Qdrant Configuration**
```yaml
# qdrant-config.yaml
storage:
  storage_path: ./storage
service:
  http_port: 6333
  grpc_port: 6334
```

**3. Memory Settings**
```bash
# For large datasets
export DJANGO_SETTINGS_MODULE=config.settings.production
export PYTHONHASHSEED=random
export MALLOC_ARENA_MAX=2
```

## 🔐 Security Considerations

### Development
- Use strong SECRET_KEY
- Keep DEBUG=True only in development
- Secure your .env file (add to .gitignore)

### Production
- Use environment variables for secrets
- Enable HTTPS/SSL
- Configure proper firewall rules
- Regular security updates
- Monitor access logs

## 📊 Monitoring & Health Checks

### Application Health
```bash
# Check application status
curl http://localhost:8000/api/health/

# Check database connectivity
python manage.py check --database default

# Check Qdrant status
curl http://localhost:6333/cluster
```

### Log Monitoring
```bash
# Django logs
tail -f logs/django.log

# Qdrant logs
docker logs qdrant

# System logs
journalctl -u ragsearch -f
```

## 🚀 Next Steps

1. **Configure Data Sources**: Set up Slack/GitHub integrations in admin panel
2. **Run Data Pipeline**: Use scripts to ingest and validate data
3. **Test Search**: Try queries in the search interface
4. **Monitor Performance**: Use validation scripts to ensure system health

For detailed usage instructions, see the [User Guide](USER_GUIDE.md).
For administration tasks, see the [Admin Guide](ADMIN_GUIDE.md).
