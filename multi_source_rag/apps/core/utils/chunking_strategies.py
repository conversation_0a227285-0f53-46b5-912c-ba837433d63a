"""
Source-aware chunking strategies configuration.

This module defines how different document sources should be processed
for optimal RAG performance.
"""

from enum import Enum
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


class ChunkingStrategy(Enum):
    """Enumeration of available chunking strategies."""

    # No chunking - documents are pre-optimized
    SKIP_CHUNKING = "skip_chunking"

    # Conversation-aware chunking for chat/messaging data
    CONVERSATION_AWARE = "conversation_aware"

    # Semantic chunking for long documents
    SEMANTIC_CHUNKING = "semantic_chunking"

    # File-based chunking for code repositories
    FILE_BASED = "file_based"

    # Section-based chunking for structured documents
    SECTION_BASED = "section_based"

    # Fixed-size chunking for generic content
    FIXED_SIZE = "fixed_size"


class SourceChunkingConfig:
    """Configuration for source-specific chunking strategies."""

    # Source type to chunking strategy mapping
    SOURCE_STRATEGIES = {
        # Slack sources - use hybrid conversation-aware chunking (pre-optimized)
        "slack": ChunkingStrategy.SKIP_CHUNKING,
        "local_slack": ChunkingStrategy.SKIP_CHUNKING,

        # GitHub sources - use content-aware strategies
        "github": ChunkingStrategy.FILE_BASED,
        "github_pr": ChunkingStrategy.SECTION_BASED,  # Section-based for PRs
        "github_issue": ChunkingStrategy.CONVERSATION_AWARE,  # Conversation-aware for issues

        # Document sources - need semantic chunking
        "confluence": ChunkingStrategy.SECTION_BASED,
        "notion": ChunkingStrategy.SECTION_BASED,
        "web": ChunkingStrategy.SEMANTIC_CHUNKING,
        "pdf": ChunkingStrategy.SEMANTIC_CHUNKING,

        # Generic file uploads
        "file": ChunkingStrategy.SEMANTIC_CHUNKING,

        # Other messaging platforms
        "discord": ChunkingStrategy.CONVERSATION_AWARE,
        "teams": ChunkingStrategy.CONVERSATION_AWARE,
    }

    # Strategy-specific parameters
    STRATEGY_PARAMS = {
        ChunkingStrategy.SKIP_CHUNKING: {
            "description": "Documents are pre-optimized with hybrid conversation-aware chunking",
            "requires_chunking": False,
            "preserve_metadata": True,
            "direct_embedding": True,
            "max_tokens": 1500,  # Hybrid approach uses higher token limits
            "conversation_gap_minutes": 30
        },

        ChunkingStrategy.CONVERSATION_AWARE: {
            "description": "Group messages by conversation threads and time proximity (hybrid approach)",
            "requires_chunking": True,
            "chunk_size": 1500,  # Increased from 2000 to match hybrid approach
            "chunk_overlap": 150,
            "preserve_threads": True,
            "time_gap_minutes": 30,
            "max_tokens": 1500
        },

        ChunkingStrategy.SEMANTIC_CHUNKING: {
            "description": "Split by semantic boundaries using NLP",
            "requires_chunking": True,
            "chunk_size": 1500,
            "chunk_overlap": 150,
            "use_sentence_boundaries": True,
            "min_chunk_size": 300
        },

        ChunkingStrategy.FILE_BASED: {
            "description": "Split by file boundaries and functions/classes",
            "requires_chunking": True,
            "chunk_size": 2500,
            "chunk_overlap": 250,
            "preserve_functions": True,
            "preserve_classes": True
        },

        ChunkingStrategy.SECTION_BASED: {
            "description": "Split by document sections and headers (content-aware for GitHub PRs)",
            "requires_chunking": True,
            "chunk_size": 2000,  # Higher limit for section-based approach
            "chunk_overlap": 200,
            "preserve_headers": True,
            "use_markdown_sections": True,
            "max_tokens": 2000
        },

        ChunkingStrategy.FIXED_SIZE: {
            "description": "Split by fixed character/token limits",
            "requires_chunking": True,
            "chunk_size": 1000,
            "chunk_overlap": 100,
            "use_token_counting": True
        }
    }

    @classmethod
    def get_strategy_for_source(cls, source_type: str) -> ChunkingStrategy:
        """
        Get the chunking strategy for a source type.

        Args:
            source_type: Type of the document source

        Returns:
            ChunkingStrategy: Strategy to use for this source type
        """
        strategy = cls.SOURCE_STRATEGIES.get(source_type, ChunkingStrategy.SEMANTIC_CHUNKING)
        logger.info(f"Selected chunking strategy '{strategy.value}' for source type '{source_type}'")
        return strategy

    @classmethod
    def get_strategy_params(cls, strategy: ChunkingStrategy) -> Dict[str, Any]:
        """
        Get parameters for a chunking strategy.

        Args:
            strategy: Chunking strategy

        Returns:
            Dict[str, Any]: Strategy parameters
        """
        return cls.STRATEGY_PARAMS.get(strategy, {})

    @classmethod
    def should_skip_chunking(cls, source_type: str) -> bool:
        """
        Check if chunking should be skipped for a source type.

        Args:
            source_type: Type of the document source

        Returns:
            bool: True if chunking should be skipped
        """
        strategy = cls.get_strategy_for_source(source_type)
        params = cls.get_strategy_params(strategy)
        return not params.get("requires_chunking", True)

    @classmethod
    def get_chunk_size(cls, source_type: str) -> int:
        """
        Get the optimal chunk size for a source type.

        Args:
            source_type: Type of the document source

        Returns:
            int: Optimal chunk size in characters
        """
        strategy = cls.get_strategy_for_source(source_type)
        params = cls.get_strategy_params(strategy)
        return params.get("chunk_size", 1500)

    @classmethod
    def should_preserve_metadata(cls, source_type: str) -> bool:
        """
        Check if metadata should be preserved during processing.

        Args:
            source_type: Type of the document source

        Returns:
            bool: True if metadata should be preserved
        """
        strategy = cls.get_strategy_for_source(source_type)
        params = cls.get_strategy_params(strategy)
        return params.get("preserve_metadata", False)


def get_chunking_strategy_info(source_type: str) -> Dict[str, Any]:
    """
    Get comprehensive chunking strategy information for a source type.

    Args:
        source_type: Type of the document source

    Returns:
        Dict[str, Any]: Complete strategy information
    """
    strategy = SourceChunkingConfig.get_strategy_for_source(source_type)
    params = SourceChunkingConfig.get_strategy_params(strategy)

    return {
        "source_type": source_type,
        "strategy": strategy.value,
        "skip_chunking": not params.get("requires_chunking", True),
        "preserve_metadata": params.get("preserve_metadata", False),
        "direct_embedding": params.get("direct_embedding", False),
        "chunk_size": params.get("chunk_size", 1500),
        "chunk_overlap": params.get("chunk_overlap", 150),
        "description": params.get("description", ""),
        "params": params
    }


# Utility functions for backward compatibility
def should_skip_chunking(source_type: str) -> bool:
    """Backward compatibility function."""
    return SourceChunkingConfig.should_skip_chunking(source_type)


def get_optimal_chunk_size(source_type: str) -> int:
    """Backward compatibility function."""
    return SourceChunkingConfig.get_chunk_size(source_type)


def get_chunking_strategy_for_source(source_type: str) -> ChunkingStrategy:
    """Get the chunking strategy for a source type."""
    return SourceChunkingConfig.get_strategy_for_source(source_type)


def create_skip_chunking_pipeline(tenant_slug: str, content_type: str = "document"):
    """
    Create a pipeline that skips chunking and directly embeds documents.

    Args:
        tenant_slug: Tenant slug for collection naming
        content_type: Content type for collection naming

    Returns:
        IngestionPipeline: Pipeline that skips chunking
    """
    from llama_index.core.ingestion import IngestionPipeline
    from apps.core.utils.collection_manager import get_collection_name
    from apps.core.utils.llama_index_vectorstore import get_vector_store
    from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content

    collection_name = get_collection_name(tenant_slug, intent=content_type)
    vector_store = get_vector_store(collection_name=collection_name)
    embed_model = get_embedding_model_for_content(content_type=content_type)

    # Create pipeline with only embedding transformation (no chunking)
    return IngestionPipeline(
        transformations=[
            embed_model,  # Only embed, don't chunk
        ],
        vector_store=vector_store
    )
