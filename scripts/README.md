# Data Management Scripts

This directory contains production-grade scripts for managing your RAG system data pipeline.

## 📁 Scripts Overview

### 🧹 `cleanup_data.py`
**Purpose**: Safely clean both Django database and Qdrant vector database.

**Features**:
- Removes all documents, chunks, embeddings, and search history
- Preserves DocumentSources (configurations)
- Cleans all Qdrant collections
- Verification step to ensure complete cleanup
- Interactive confirmation for safety

**Usage**:
```bash
cd multi_source_rag
python ../scripts/cleanup_data.py
```

### 📥 `ingest_data.py`
**Purpose**: Production-grade data ingestion using the same services as production.

**Features**:
- Uses `IngestionService` and `UnifiedLlamaIndexIngestionService`
- Creates proper `DocumentProcessingJob` records
- Interactive source selection (all sources or specific source)
- Comprehensive statistics and timing
- Error handling and recovery
- Production batch sizes and patterns

**Usage**:
```bash
cd multi_source_rag
python ../scripts/ingest_data.py
```

### 🔍 `validate_data_consistency.py`
**Purpose**: Comprehensive validation of data integrity and consistency.

**Features**:
- Django database health check
- Qdrant database connectivity and health
- Cross-database consistency validation
- Source-specific consistency checks
- Orphaned record detection
- Generates detailed JSON reports
- Uses improved validation methods from ingestion service

**Usage**:
```bash
cd multi_source_rag
python ../scripts/validate_data_consistency.py
```

### 🔄 `data_pipeline.py`
**Purpose**: Master orchestration script that runs all three operations in sequence.

**Features**:
- Runs cleanup → ingestion → validation
- Interactive confirmation
- Comprehensive timing and status reporting
- Stops on failures for safety
- Complete pipeline overview

**Usage**:
```bash
cd multi_source_rag
python ../scripts/data_pipeline.py
```

## 🚀 Quick Start

### Option 1: Complete Pipeline (Recommended)
```bash
cd multi_source_rag
python ../scripts/data_pipeline.py
```

### Option 2: Individual Steps
```bash
# Step 1: Clean existing data
python ../scripts/cleanup_data.py

# Step 2: Ingest fresh data
python ../scripts/ingest_data.py

# Step 3: Validate consistency
python ../scripts/validate_data_consistency.py
```

## 📊 What Each Script Does

### Cleanup Script
- ✅ Deletes all SearchResults, Citations, SearchQueries
- ✅ Deletes all EmbeddingMetadata records
- ✅ Deletes all DocumentChunks
- ✅ Deletes all RawDocuments
- ✅ Deletes all Qdrant collections
- ✅ Preserves DocumentSources and user accounts
- ✅ Verifies complete cleanup

### Ingestion Script
- ✅ Uses production `IngestionService`
- ✅ Creates `DocumentProcessingJob` records
- ✅ Processes documents in production batch sizes
- ✅ Handles errors with proper recovery
- ✅ Provides detailed statistics
- ✅ Interactive source selection

### Validation Script
- ✅ Checks Django database integrity
- ✅ Validates Qdrant connectivity and health
- ✅ Verifies cross-database consistency
- ✅ Validates each source individually
- ✅ Detects orphaned records
- ✅ Generates timestamped JSON reports
- ✅ Provides comprehensive health scoring

## 🔧 Prerequisites

1. **Django Environment**: Scripts must be run from `multi_source_rag` directory
2. **Qdrant Running**: Vector database must be accessible on localhost:6333
3. **User Account**: User with email `<EMAIL>` should exist
4. **Document Sources**: At least one DocumentSource should be configured
5. **Data Files**: For ingestion, ensure your data sources are properly configured

## 📈 Expected Output

### Successful Pipeline Run
```
🔄 COMPLETE DATA PIPELINE
============================================================
🚀 STEP 1: DATA CLEANUP
✅ Deleted 1/1 collections
✅ Deleted 545 documents
✅ SUCCESS: STEP 1: DATA CLEANUP

🚀 STEP 2: DATA INGESTION  
✅ Processed: 545 documents
✅ SUCCESS: STEP 2: DATA INGESTION

🚀 STEP 3: DATA VALIDATION
✅ Django database: Healthy
✅ Qdrant database: Healthy
✅ Cross-DB consistency: Consistent
✅ SUCCESS: STEP 3: DATA VALIDATION

🎉 PIPELINE SUCCESSFUL!
```

## 🚨 Troubleshooting

### Common Issues

1. **Qdrant Connection Failed**
   - Ensure Qdrant is running: `docker ps | grep qdrant`
   - Check port 6333 is accessible

2. **No Document Sources Found**
   - Create DocumentSources via Django admin
   - Or use management commands to create sources

3. **Permission Errors**
   - Ensure user `<EMAIL>` exists
   - Check database permissions

4. **Memory Issues During Ingestion**
   - Reduce batch size in ingestion script
   - Monitor system resources

### Getting Help

1. Check script logs for detailed error messages
2. Review validation reports for specific issues
3. Use individual scripts to isolate problems
4. Check Django logs and Qdrant logs

## 📝 Notes

- **Safety First**: All scripts include confirmation prompts
- **Production Ready**: Uses the same services as production environment
- **Comprehensive**: Covers the complete data lifecycle
- **Monitoring**: Provides detailed reporting and validation
- **Recovery**: Includes error handling and fallback mechanisms

These scripts solve the data consistency issues you experienced and provide a robust foundation for maintaining your RAG system.
