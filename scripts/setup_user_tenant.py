# Create superuser and tenant
from django.contrib.auth.models import User
from apps.accounts.models import Tenant, UserProfile

# Create superuser
user = User.objects.create_superuser(
    username="mahesh",
    email="<EMAIL>",
    password="admin123"
)
print(f"Created superuser: {user.username}")

# Create tenant
tenant = Tenant.objects.create(
    name="Stride Technologies",
    slug="stride"
)
print(f"Created tenant: {tenant.name}")

# Create user profile
profile = UserProfile.objects.create(
    user=user,
    tenant=tenant
)
print(f"Created user profile for {user.username}")

print("✅ Setup complete!")
