#!/usr/bin/env python3
"""
End-to-End GitHub Integration Test

This script performs comprehensive testing of the GitHub integration:
1. Interface functionality
2. Data ingestion
3. Data validation
4. Search functionality
5. System health checks

Usage:
    python scripts/test_github_end_to_end.py [--quick] [--verbose]
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime

def main():
    """Main function."""
    print("🚀 GitHub Integration - End-to-End Test Suite")
    print("=" * 70)

    # Parse arguments
    parser = argparse.ArgumentParser(description='End-to-End GitHub Integration Test')
    parser.add_argument('--quick', action='store_true', help='Run quick tests only')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    args = parser.parse_args()

    # Run test suite
    tester = GitHubEndToEndTester(quick=args.quick, verbose=args.verbose)
    success = tester.run_tests()

    if success:
        print("\n🎉 All tests passed! GitHub integration is working perfectly.")
    else:
        print("\n❌ Some tests failed. Please check the output above.")

    sys.exit(0 if success else 1)


class GitHubEndToEndTester:
    """Comprehensive end-to-end tester for GitHub integration."""

    def __init__(self, quick: bool = False, verbose: bool = False):
        self.quick = quick
        self.verbose = verbose
        self.test_results = []
        self.start_time = datetime.now()

    def run_tests(self):
        """Run the complete test suite."""
        print("🔍 Starting End-to-End GitHub Integration Tests")
        print("=" * 60)

        # Test 1: Interface functionality
        if not self.test_github_interface():
            return False

        # Test 2: Data ingestion (quick mode uses smaller dataset)
        if not self.test_data_ingestion():
            return False

        # Test 3: Data validation
        if not self.test_data_validation():
            return False

        # Test 4: Search functionality (if not quick mode)
        if not self.quick:
            if not self.test_search_functionality():
                return False

        # Test 5: System health
        if not self.test_system_health():
            return False

        # Generate final report
        self.generate_test_report()
        return all(result['passed'] for result in self.test_results)

    def test_github_interface(self):
        """Test GitHub interface functionality."""
        print("\n🔧 Test 1: GitHub Interface Functionality")
        print("-" * 50)

        try:
            result = subprocess.run(
                ["python", "scripts/test_github_simple.py"],
                capture_output=True, text=True, cwd=os.getcwd()
            )

            if result.returncode == 0:
                print("✅ GitHub interface test passed")
                # Parse output for document count
                output_lines = result.stdout.split('\n')
                doc_count = 0
                for line in output_lines:
                    if "Fetched" in line and "documents" in line:
                        try:
                            doc_count = int(line.split()[1])
                            break
                        except:
                            pass

                self.test_results.append({
                    'test': 'GitHub Interface',
                    'passed': True,
                    'details': f'Fetched {doc_count} documents successfully'
                })
                return True
            else:
                print("❌ GitHub interface test failed")
                if self.verbose:
                    print(f"Error: {result.stderr}")
                self.test_results.append({
                    'test': 'GitHub Interface',
                    'passed': False,
                    'details': 'Interface test failed'
                })
                return False

        except Exception as e:
            print(f"❌ Error running interface test: {str(e)}")
            self.test_results.append({
                'test': 'GitHub Interface',
                'passed': False,
                'details': f'Exception: {str(e)}'
            })
            return False

    def test_data_ingestion(self):
        """Test data ingestion functionality."""
        print("\n📥 Test 2: Data Ingestion")
        print("-" * 50)

        try:
            # Use smaller dataset for quick tests
            days = 7 if self.quick else 30
            content_types = "pull_request" if self.quick else "pull_request,issue,release"

            cmd = [
                "python", "scripts/ingest_github_production.py",
                "--repo", "Compiify/Yellowstone",
                "--days", str(days),
                "--content-types", content_types
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())

            if result.returncode == 0:
                print("✅ Data ingestion test passed")
                
                # Parse output for stats
                output_lines = result.stdout.split('\n')
                processed = 0
                failed = 0
                for line in output_lines:
                    if "Total Processed:" in line:
                        processed = int(line.split(":")[1].strip().split()[0])
                    elif "Total Failed:" in line:
                        failed = int(line.split(":")[1].strip().split()[0])

                self.test_results.append({
                    'test': 'Data Ingestion',
                    'passed': True,
                    'details': f'Processed {processed} documents, {failed} failed'
                })
                return True
            else:
                print("❌ Data ingestion test failed")
                if self.verbose:
                    print(f"Error: {result.stderr}")
                self.test_results.append({
                    'test': 'Data Ingestion',
                    'passed': False,
                    'details': 'Ingestion failed'
                })
                return False

        except Exception as e:
            print(f"❌ Error running ingestion test: {str(e)}")
            self.test_results.append({
                'test': 'Data Ingestion',
                'passed': False,
                'details': f'Exception: {str(e)}'
            })
            return False

    def test_data_validation(self):
        """Test data validation."""
        print("\n🔍 Test 3: Data Validation")
        print("-" * 50)

        try:
            result = subprocess.run(
                ["python", "scripts/validate_ingestion.py"],
                capture_output=True, text=True, cwd=os.getcwd()
            )

            # Validation script returns 0 for success, 1 for issues
            if result.returncode == 0:
                print("✅ Data validation test passed")
                self.test_results.append({
                    'test': 'Data Validation',
                    'passed': True,
                    'details': 'No data quality issues found'
                })
                return True
            else:
                print("⚠️  Data validation found issues (may be acceptable)")
                # Parse output to see if issues are critical
                output_lines = result.stdout.split('\n')
                issue_count = 0
                for line in output_lines:
                    if "Issues Found" in line:
                        try:
                            issue_count = int(line.split("(")[1].split(")")[0])
                            break
                        except:
                            pass

                # Consider test passed if only minor issues
                passed = issue_count <= 2  # Allow up to 2 minor issues
                status = "✅" if passed else "❌"
                print(f"{status} Found {issue_count} issues ({'acceptable' if passed else 'too many'})")
                
                self.test_results.append({
                    'test': 'Data Validation',
                    'passed': passed,
                    'details': f'Found {issue_count} data quality issues'
                })
                return passed

        except Exception as e:
            print(f"❌ Error running validation test: {str(e)}")
            self.test_results.append({
                'test': 'Data Validation',
                'passed': False,
                'details': f'Exception: {str(e)}'
            })
            return False

    def test_search_functionality(self):
        """Test search functionality with GitHub data."""
        print("\n🔍 Test 4: Search Functionality")
        print("-" * 50)

        try:
            # Check if search test script exists
            if os.path.exists("scripts/test_search.py"):
                result = subprocess.run(
                    ["python", "scripts/test_search.py"],
                    capture_output=True, text=True, cwd=os.getcwd()
                )

                if result.returncode == 0:
                    print("✅ Search functionality test passed")
                    self.test_results.append({
                        'test': 'Search Functionality',
                        'passed': True,
                        'details': 'Search working with GitHub data'
                    })
                    return True
                else:
                    print("❌ Search functionality test failed")
                    if self.verbose:
                        print(f"Error: {result.stderr}")
                    self.test_results.append({
                        'test': 'Search Functionality',
                        'passed': False,
                        'details': 'Search test failed'
                    })
                    return False
            else:
                print("⚠️  Search test script not found, skipping")
                self.test_results.append({
                    'test': 'Search Functionality',
                    'passed': True,
                    'details': 'Test script not available (skipped)'
                })
                return True

        except Exception as e:
            print(f"❌ Error running search test: {str(e)}")
            self.test_results.append({
                'test': 'Search Functionality',
                'passed': False,
                'details': f'Exception: {str(e)}'
            })
            return False

    def test_system_health(self):
        """Test overall system health."""
        print("\n🏥 Test 5: System Health")
        print("-" * 50)

        try:
            # Check if data consistency script exists
            if os.path.exists("scripts/validate_data_consistency.py"):
                result = subprocess.run(
                    ["python", "scripts/validate_data_consistency.py"],
                    capture_output=True, text=True, cwd=os.getcwd()
                )

                # This script may return warnings but still be healthy
                print("✅ System health check completed")
                self.test_results.append({
                    'test': 'System Health',
                    'passed': True,
                    'details': 'System health validated'
                })
                return True
            else:
                print("⚠️  System health script not found, skipping")
                self.test_results.append({
                    'test': 'System Health',
                    'passed': True,
                    'details': 'Health script not available (skipped)'
                })
                return True

        except Exception as e:
            print(f"❌ Error running health check: {str(e)}")
            self.test_results.append({
                'test': 'System Health',
                'passed': False,
                'details': f'Exception: {str(e)}'
            })
            return False

    def generate_test_report(self):
        """Generate final test report."""
        end_time = datetime.now()
        duration = end_time - self.start_time

        print("\n" + "=" * 70)
        print("📊 END-TO-END TEST REPORT")
        print("=" * 70)

        print(f"⏱️  Test Duration: {duration}")
        print(f"🧪 Tests Run: {len(self.test_results)}")

        passed_tests = [r for r in self.test_results if r['passed']]
        failed_tests = [r for r in self.test_results if not r['passed']]

        print(f"✅ Passed: {len(passed_tests)}")
        print(f"❌ Failed: {len(failed_tests)}")

        if passed_tests:
            print(f"\n✅ Passed Tests:")
            for test in passed_tests:
                print(f"   • {test['test']}: {test['details']}")

        if failed_tests:
            print(f"\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"   • {test['test']}: {test['details']}")

        # Overall status
        success_rate = len(passed_tests) / len(self.test_results) * 100
        print(f"\n📈 Success Rate: {success_rate:.1f}%")

        if success_rate == 100:
            print(f"\n🎉 Perfect! All tests passed - GitHub integration is production-ready!")
        elif success_rate >= 80:
            print(f"\n✅ Good! Most tests passed - GitHub integration is working well")
        else:
            print(f"\n⚠️  Issues detected - please review failed tests")

        print(f"\n🎯 Next Steps:")
        if success_rate == 100:
            print(f"   • GitHub integration is ready for production use")
            print(f"   • Consider running regular health checks")
            print(f"   • Monitor ingestion performance in production")
        else:
            print(f"   • Address failed tests before production deployment")
            print(f"   • Review error logs and fix issues")
            print(f"   • Re-run tests after fixes")


if __name__ == "__main__":
    main()
