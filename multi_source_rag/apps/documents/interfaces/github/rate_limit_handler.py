"""
GitHub API rate limit handler.
"""

import logging
import time
from datetime import datetime

from github import Github

# Set up logging
logger = logging.getLogger(__name__)


class RateLimitHandler:
    """
    Handler for GitHub API rate limiting.
    """

    def __init__(self, max_retries: int = 3, retry_delay: int = 5):
        """
        Initialize the rate limit handler.

        Args:
            max_retries: Maximum number of retries
            retry_delay: Delay between retries in seconds
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def handle_rate_limit(self, client: Github) -> bool:
        """
        Handle rate limit by waiting for reset.

        Args:
            client: GitHub client

        Returns:
            bool: True if rate limit was handled, False otherwise
        """
        try:
            rate_limit = client.get_rate_limit()
            reset_timestamp = rate_limit.core.reset.timestamp()
            current_timestamp = datetime.now().timestamp()

            # Calculate wait time
            wait_time = reset_timestamp - current_timestamp

            if wait_time > 0:
                logger.warning(
                    f"GitHub API rate limit reached. Waiting {wait_time:.2f} seconds for reset."
                )
                time.sleep(wait_time + 1)  # Add 1 second buffer
                return True

            return False
        except Exception as e:
            logger.error(f"Error handling rate limit: {str(e)}")
            return False
