#!/usr/bin/env python3
"""
Production GitHub Data Ingestion Script

This script ingests GitHub data using production-ready services with proper
error handling, embedding model consistency, and comprehensive reporting.

Usage:
    python scripts/ingest_github_production.py [--repo REPO] [--days DAYS] [--clean]

Options:
    --repo      Repository in format owner/repo (default: Compiify/Yellowstone)
    --days      Number of days to fetch (default: 365)
    --clean     Clean existing data before ingestion
    --content-types  Content types to fetch (default: pull_request,issue,release)
"""

import os
import sys
import argparse
from datetime import datetime

def main():
    """Main function."""
    print("🚀 Starting Production GitHub Data Ingestion")
    sys.stdout.flush()

    # Add the project root to Python path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

    print("📦 Setting up Django...")
    sys.stdout.flush()
    import django
    django.setup()

    print("📚 Importing services...")
    sys.stdout.flush()
    from django.contrib.auth.models import User
    from apps.accounts.models import Tenant
    from apps.documents.models import DocumentSource, RawDocument
    from apps.documents.interfaces.factory import DocumentSourceFactory

    # Parse arguments
    parser = argparse.ArgumentParser(description='Production GitHub Data Ingestion Script')
    parser.add_argument('--repo', default='Compiify/Yellowstone', help='Repository in format owner/repo')
    parser.add_argument('--days', type=int, default=365, help='Number of days to fetch')
    parser.add_argument('--clean', action='store_true', help='Clean existing data before ingestion')
    parser.add_argument('--content-types', default='pull_request,issue,release', help='Content types to fetch')

    args = parser.parse_args()
    print(f"📋 Arguments: repo={args.repo}, days={args.days}, clean={args.clean}")
    sys.stdout.flush()

    # Parse repository
    try:
        owner, repo_name = args.repo.split("/")
    except ValueError:
        print(f"❌ Invalid repository format: {args.repo}. Expected format: owner/repo")
        sys.exit(1)

    content_types = args.content_types.split(',')

    # Run ingestion
    ingester = GitHubProductionIngester()
    success = ingester.run(
        owner=owner,
        repo=repo_name,
        days=args.days,
        content_types=content_types,
        clean_data=args.clean
    )

    sys.exit(0 if success else 1)


class GitHubProductionIngester:
    """Production-ready GitHub data ingestion using actual production services."""

    def __init__(self):
        self.tenant = None
        self.user = None
        self.stats = {
            "total_processed": 0,
            "total_failed": 0,
            "start_time": datetime.now()
        }
    
    def setup_environment(self):
        """Setup tenant and user."""
        print("🔧 Setting up production environment...")

        from django.contrib.auth.models import User
        from apps.accounts.models import Tenant

        # Get tenant and user (using the same ones as in production)
        try:
            self.tenant = Tenant.objects.get(slug='default')
            self.user = User.objects.get(username='mahesh')
            print(f"✅ Using tenant: {self.tenant.name}")
            print(f"✅ Using user: {self.user.username}")
        except (Tenant.DoesNotExist, User.DoesNotExist) as e:
            print(f"❌ Error: {str(e)}")
            print("Please ensure tenant 'default' and user 'mahesh' exist.")
            return False

        return True
    
    def clean_existing_data(self, owner: str, repo: str):
        """Clean existing data for the repository."""
        print("🧹 Cleaning existing data...")
        
        from apps.documents.models import DocumentSource, RawDocument

        source_name = f"GitHub: {owner}/{repo}"
        try:
            source = DocumentSource.objects.get(tenant=self.tenant, name=source_name)
            count = RawDocument.objects.filter(source=source).count()
            if count > 0:
                RawDocument.objects.filter(source=source).delete()
                print(f"  🗑️  Deleted {count} documents from {source.name}")
            else:
                print(f"  ℹ️  No existing documents found for {source.name}")
        except DocumentSource.DoesNotExist:
            print(f"  ℹ️  No existing source found for {source_name}")
    
    def ingest_github_data(self, owner: str, repo: str, days: int, content_types: list):
        """Ingest GitHub data using the interface directly."""
        print(f"\n🐙 Ingesting GitHub Data from {owner}/{repo}")
        print("=" * 60)
        
        from apps.documents.models import DocumentSource, RawDocument
        from apps.documents.interfaces.factory import DocumentSourceFactory

        # Create or get GitHub source
        source_name = f"GitHub: {owner}/{repo}"
        source, created = DocumentSource.objects.get_or_create(
            tenant=self.tenant,
            name=source_name,
            source_type="github",
            defaults={
                "config": {
                    "token": "****************************************",
                    "owner": owner,
                    "repo": repo,
                    "content_types": content_types
                },
                "is_active": True
            }
        )
        
        if created:
            print(f"✅ Created new GitHub source: {source.name}")
        else:
            print(f"✅ Using existing GitHub source: {source.name}")
        
        try:
            # Create GitHub interface directly
            github_interface = DocumentSourceFactory.create_source("github", source.config)
            print(f"✅ Initialized GitHub interface")
            
            # Fetch documents
            print(f"📥 Fetching documents for the last {days} days...")
            documents = github_interface.fetch_documents(
                days=days,
                max_per_type=50  # Reasonable limit for production
            )
            
            print(f"✅ Fetched {len(documents)} documents from GitHub")
            
            # Store documents directly in Django
            processed = 0
            failed = 0
            
            for doc in documents:
                try:
                    # Create or update raw document
                    raw_doc, created = RawDocument.objects.get_or_create(
                        source=source,
                        external_id=doc.get('external_id', doc['id']),
                        defaults={
                            'tenant': self.tenant,  # Add tenant
                            'title': doc['title'],
                            'content_type': doc.get('content_type', 'github_pr'),
                            'metadata': doc.get('metadata', {}),
                            'permalink': doc.get('metadata', {}).get('html_url'),
                            'created_at': doc.get('created_at'),
                            'updated_at': doc.get('updated_at')
                        }
                    )
                    
                    # Store content separately
                    from apps.documents.models import DocumentContent
                    content_obj, content_created = DocumentContent.objects.get_or_create(
                        document=raw_doc,
                        defaults={
                            'content': doc['content'],
                            'content_format': 'markdown',
                            'content_size': len(doc['content'])
                        }
                    )
                    
                    if not content_created and content_obj.content != doc['content']:
                        content_obj.content = doc['content']
                        content_obj.content_size = len(doc['content'])
                        content_obj.save()
                    
                    processed += 1
                    
                    if processed % 10 == 0:
                        print(f"  📝 Processed {processed}/{len(documents)} documents...")
                    
                except Exception as e:
                    print(f"  ❌ Error processing document {doc.get('id', 'unknown')}: {str(e)}")
                    failed += 1
            
            print(f"✅ Stored {processed} documents in database")
            print(f"❌ Failed to store {failed} documents")
            
            # Display statistics
            stats = github_interface.get_stats()
            print(f"\n📊 GitHub API Statistics:")
            print(f"   Processing time: {stats['processing_time']:.2f}s")
            print(f"   By content type:")
            for content_type, count in stats['by_content_type'].items():
                print(f"     - {content_type}: {count}")
            
            return processed, failed
            
        except Exception as e:
            print(f"❌ Error during GitHub ingestion: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0, 1
    
    def generate_report(self, processed: int, failed: int):
        """Generate ingestion report."""
        end_time = datetime.now()
        duration = end_time - self.stats["start_time"]
        
        print("\n" + "=" * 60)
        print("📊 GITHUB INGESTION REPORT")
        print("=" * 60)
        print(f"⏱️  Duration: {duration}")
        print(f"✅ Total Processed: {processed} documents")
        print(f"❌ Total Failed: {failed} documents")
        
        if processed > 0:
            success_rate = (processed / (processed + failed)) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")
        
        print("\n🎉 GitHub ingestion completed!")
    
    def run(self, owner: str, repo: str, days: int, content_types: list, clean_data: bool = False):
        """Run the GitHub ingestion process."""
        print("🚀 Starting Production GitHub Data Ingestion")
        print("=" * 60)
        
        # Setup environment
        if not self.setup_environment():
            return False
        
        # Clean data if requested
        if clean_data:
            self.clean_existing_data(owner, repo)
        
        # Ingest data
        processed, failed = self.ingest_github_data(owner, repo, days, content_types)
        
        # Generate report
        self.generate_report(processed, failed)
        
        return processed > 0


if __name__ == "__main__":
    main()
