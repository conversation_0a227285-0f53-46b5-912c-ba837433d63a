#!/usr/bin/env python3
"""
Comprehensive UI and Response Quality Validation Script

This script validates:
1. Entity-specific query accuracy
2. Time range display functionality
3. UI formatting and structure
4. Response quality and readability
5. Citation cleanliness
6. End-to-end user experience

Usage:
    cd /Users/<USER>/Desktop/RAGSearch/multi_source_rag
    python ../scripts/comprehensive_ui_validation.py
"""

import os
import sys
import django
import re
import json
from datetime import datetime

# Setup Django environment
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.rag_service import RAGService
from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.search.models import SearchResult


def test_entity_specific_queries():
    """Test entity-specific query accuracy and response quality."""
    print("🎯 Testing Entity-Specific Query Accuracy...")
    
    tenant = Tenant.objects.first()
    user = User.objects.first()
    
    if not tenant or not user:
        print("❌ No tenant or user found")
        return
    
    rag_service = RAGService(tenant_slug=tenant.slug, user=user)
    
    test_cases = [
        {
            "query": "list issues reported on Curana",
            "expected_entity": "curana",
            "expected_type": "list_issues"
        },
        {
            "query": "what is the latest on Tithely",
            "expected_entity": "tithely",
            "expected_type": "latest_updates"
        },
        {
            "query": "show me problems with Amanda",
            "expected_entity": "amanda",
            "expected_type": "list_issues"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n📝 Testing: '{test_case['query']}'")
        
        try:
            search_result, retrieved_docs = rag_service.search(
                query_text=test_case['query'],
                top_k=5,
                min_relevance_score=0.10,
                use_hybrid_search=True,
                use_enhanced_prompts=True
            )
            
            # Validate entity focus
            response_lower = search_result.generated_answer.lower()
            entity_focus = test_case['expected_entity'] in response_lower
            
            # Check response structure
            has_summary = "**Summary:**" in search_result.generated_answer
            has_detailed_section = "**Detailed" in search_result.generated_answer
            has_bullet_points = "•" in search_result.generated_answer
            has_bold_dates = bool(re.search(r'\*\*\w+ \d+, \d+\*\*', search_result.generated_answer))
            
            # Check citation cleanliness
            has_inline_citations = bool(re.search(r'\[\d+\]', search_result.generated_answer))
            
            # Check time range
            time_range = None
            if hasattr(search_result, 'metadata') and search_result.metadata:
                time_range = search_result.metadata.get('time_range')
            
            result = {
                'query': test_case['query'],
                'entity_focus': entity_focus,
                'response_structure': {
                    'has_summary': has_summary,
                    'has_detailed_section': has_detailed_section,
                    'has_bullet_points': has_bullet_points,
                    'has_bold_dates': has_bold_dates
                },
                'citation_clean': not has_inline_citations,
                'time_range_available': time_range is not None,
                'time_range': time_range,
                'citations_count': search_result.citations.count(),
                'response_length': len(search_result.generated_answer),
                'response_preview': search_result.generated_answer[:200] + "..."
            }
            
            results.append(result)
            
            # Print validation results
            print(f"    ✅ Entity Focus: {'✓' if entity_focus else '✗'}")
            print(f"    ✅ Response Structure: {sum(result['response_structure'].values())}/4 elements")
            print(f"    ✅ Citation Clean: {'✓' if result['citation_clean'] else '✗'}")
            print(f"    ✅ Time Range: {'✓' if result['time_range_available'] else '✗'}")
            print(f"    📊 Citations: {result['citations_count']}")
            print(f"    📝 Length: {result['response_length']} chars")
            
            if time_range:
                print(f"    📅 Time Range: {time_range['formatted_range']}")
                print(f"    🕐 Latest: {time_range['relative_latest']}")
                print(f"    📊 Span: {time_range['days_span']} days")
            
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            results.append({
                'query': test_case['query'],
                'error': str(e)
            })
    
    return results


def test_ui_formatting_quality():
    """Test UI formatting and readability."""
    print("\n🎨 Testing UI Formatting Quality...")
    
    # Get recent search results
    recent_results = SearchResult.objects.order_by('-timestamp')[:3]
    
    formatting_scores = []
    
    for result in recent_results:
        print(f"\n📋 Analyzing Search Result ID: {result.id}")
        print(f"    Query: {result.search_query.query_text[:50]}...")
        
        response = result.generated_answer
        
        # Check formatting elements
        formatting_checks = {
            'has_headers': bool(re.search(r'\*\*[^*]+\*\*:', response)),
            'has_bullet_points': '•' in response,
            'has_bold_text': '**' in response,
            'proper_spacing': '\n\n' in response,
            'no_citation_noise': not bool(re.search(r'\[\d+\]', response)),
            'readable_dates': bool(re.search(r'\*\*\w+ \d+, \d+\*\*', response)),
            'structured_content': 'Summary:' in response or 'Issues:' in response
        }
        
        score = sum(formatting_checks.values())
        total_checks = len(formatting_checks)
        
        formatting_scores.append({
            'result_id': result.id,
            'score': score,
            'total': total_checks,
            'percentage': (score / total_checks) * 100,
            'checks': formatting_checks
        })
        
        print(f"    📊 Formatting Score: {score}/{total_checks} ({(score/total_checks)*100:.1f}%)")
        
        for check, passed in formatting_checks.items():
            status = "✓" if passed else "✗"
            print(f"        {status} {check.replace('_', ' ').title()}")
    
    return formatting_scores


def test_time_range_accuracy():
    """Test time range calculation accuracy."""
    print("\n🕒 Testing Time Range Accuracy...")
    
    # Get search results with time range data
    results_with_time_range = SearchResult.objects.filter(
        metadata__time_range__isnull=False
    ).order_by('-timestamp')[:5]
    
    time_range_tests = []
    
    for result in results_with_time_range:
        time_range = result.metadata.get('time_range')
        if not time_range:
            continue
            
        print(f"\n📅 Time Range Analysis for Result ID: {result.id}")
        print(f"    Query: {result.search_query.query_text[:50]}...")
        
        # Validate time range structure
        required_fields = ['earliest', 'latest', 'formatted_range', 'relative_latest', 'days_span', 'document_count']
        has_all_fields = all(field in time_range for field in required_fields)
        
        # Validate date formats
        try:
            earliest = datetime.fromisoformat(time_range['earliest'])
            latest = datetime.fromisoformat(time_range['latest'])
            date_order_correct = earliest <= latest
        except (ValueError, KeyError):
            date_order_correct = False
        
        # Validate relative time descriptions
        relative_latest = time_range.get('relative_latest', '')
        valid_relative_terms = ['today', 'yesterday', 'days ago', 'week', 'month', 'year']
        has_valid_relative = any(term in relative_latest for term in valid_relative_terms)
        
        test_result = {
            'result_id': result.id,
            'has_all_fields': has_all_fields,
            'date_order_correct': date_order_correct,
            'has_valid_relative': has_valid_relative,
            'time_range': time_range
        }
        
        time_range_tests.append(test_result)
        
        print(f"    ✅ Complete Structure: {'✓' if has_all_fields else '✗'}")
        print(f"    ✅ Date Order: {'✓' if date_order_correct else '✗'}")
        print(f"    ✅ Valid Relative Time: {'✓' if has_valid_relative else '✗'}")
        print(f"    📊 Range: {time_range.get('formatted_range', 'N/A')}")
        print(f"    🕐 Latest: {time_range.get('relative_latest', 'N/A')}")
        print(f"    📈 Span: {time_range.get('days_span', 'N/A')} days")
    
    return time_range_tests


def test_response_quality_metrics():
    """Test overall response quality metrics."""
    print("\n📊 Testing Response Quality Metrics...")
    
    recent_results = SearchResult.objects.order_by('-timestamp')[:5]
    
    quality_metrics = []
    
    for result in recent_results:
        print(f"\n📈 Quality Analysis for Result ID: {result.id}")
        
        response = result.generated_answer
        
        # Calculate quality metrics
        word_count = len(response.split())
        sentence_count = len(re.findall(r'[.!?]+', response))
        avg_sentence_length = word_count / max(sentence_count, 1)
        
        # Check for professional language
        has_professional_tone = not bool(re.search(r'\b(um|uh|like|you know)\b', response.lower()))
        
        # Check for specific details
        has_dates = bool(re.search(r'\d{4}', response))
        has_names = bool(re.search(r'\b[A-Z][a-z]+ [A-Z][a-z]+\b', response))
        has_specific_details = bool(re.search(r'\b(error|issue|problem|bug|fix|resolve)\b', response.lower()))
        
        # Check structure quality
        has_clear_structure = bool(re.search(r'\*\*[^*]+\*\*:', response))
        has_logical_flow = 'Summary:' in response and ('Issues:' in response or 'Details:' in response)
        
        metrics = {
            'result_id': result.id,
            'word_count': word_count,
            'sentence_count': sentence_count,
            'avg_sentence_length': avg_sentence_length,
            'has_professional_tone': has_professional_tone,
            'has_dates': has_dates,
            'has_names': has_names,
            'has_specific_details': has_specific_details,
            'has_clear_structure': has_clear_structure,
            'has_logical_flow': has_logical_flow,
            'citations_count': result.citations.count()
        }
        
        quality_metrics.append(metrics)
        
        print(f"    📝 Word Count: {word_count}")
        print(f"    📄 Sentences: {sentence_count}")
        print(f"    📏 Avg Sentence Length: {avg_sentence_length:.1f} words")
        print(f"    🎯 Professional Tone: {'✓' if has_professional_tone else '✗'}")
        print(f"    📅 Has Dates: {'✓' if has_dates else '✗'}")
        print(f"    👤 Has Names: {'✓' if has_names else '✗'}")
        print(f"    🔍 Specific Details: {'✓' if has_specific_details else '✗'}")
        print(f"    📋 Clear Structure: {'✓' if has_clear_structure else '✗'}")
        print(f"    🔄 Logical Flow: {'✓' if has_logical_flow else '✗'}")
        print(f"    📚 Citations: {result.citations.count()}")
    
    return quality_metrics


def generate_validation_report(entity_results, formatting_scores, time_range_tests, quality_metrics):
    """Generate a comprehensive validation report."""
    print("\n" + "="*80)
    print("📋 COMPREHENSIVE VALIDATION REPORT")
    print("="*80)
    
    # Entity Query Accuracy Summary
    print("\n🎯 ENTITY QUERY ACCURACY")
    entity_success_rate = sum(1 for r in entity_results if r.get('entity_focus', False)) / len(entity_results) * 100
    print(f"    Success Rate: {entity_success_rate:.1f}%")
    
    # UI Formatting Summary
    print("\n🎨 UI FORMATTING QUALITY")
    avg_formatting_score = sum(s['percentage'] for s in formatting_scores) / len(formatting_scores)
    print(f"    Average Score: {avg_formatting_score:.1f}%")
    
    # Time Range Feature Summary
    print("\n🕒 TIME RANGE FEATURE")
    time_range_success = sum(1 for t in time_range_tests if t['has_all_fields'] and t['date_order_correct']) / max(len(time_range_tests), 1) * 100
    print(f"    Success Rate: {time_range_success:.1f}%")
    
    # Response Quality Summary
    print("\n📊 RESPONSE QUALITY")
    avg_word_count = sum(m['word_count'] for m in quality_metrics) / len(quality_metrics)
    professional_rate = sum(1 for m in quality_metrics if m['has_professional_tone']) / len(quality_metrics) * 100
    structure_rate = sum(1 for m in quality_metrics if m['has_clear_structure']) / len(quality_metrics) * 100
    
    print(f"    Average Word Count: {avg_word_count:.0f}")
    print(f"    Professional Tone: {professional_rate:.1f}%")
    print(f"    Clear Structure: {structure_rate:.1f}%")
    
    # Overall Assessment
    print("\n🏆 OVERALL ASSESSMENT")
    overall_score = (entity_success_rate + avg_formatting_score + time_range_success + professional_rate + structure_rate) / 5
    
    if overall_score >= 90:
        grade = "A+ (Excellent)"
    elif overall_score >= 80:
        grade = "A (Very Good)"
    elif overall_score >= 70:
        grade = "B (Good)"
    elif overall_score >= 60:
        grade = "C (Acceptable)"
    else:
        grade = "D (Needs Improvement)"
    
    print(f"    Overall Score: {overall_score:.1f}% - {grade}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS")
    if entity_success_rate < 90:
        print("    - Improve entity extraction and focus mechanisms")
    if avg_formatting_score < 90:
        print("    - Enhance response formatting templates")
    if time_range_success < 90:
        print("    - Improve time range calculation reliability")
    if professional_rate < 90:
        print("    - Refine LLM prompts for professional tone")
    if structure_rate < 90:
        print("    - Improve response structure consistency")
    
    if overall_score >= 90:
        print("    ✅ System performing excellently - ready for production!")


def main():
    """Run comprehensive UI and response quality validation."""
    print("🚀 Comprehensive UI and Response Quality Validation")
    print("="*60)
    
    # Run all validation tests
    entity_results = test_entity_specific_queries()
    formatting_scores = test_ui_formatting_quality()
    time_range_tests = test_time_range_accuracy()
    quality_metrics = test_response_quality_metrics()
    
    # Generate comprehensive report
    generate_validation_report(entity_results, formatting_scores, time_range_tests, quality_metrics)
    
    print("\n✅ Validation Complete!")
    print("\nTo test in browser:")
    print("1. Go to http://127.0.0.1:8000/search/")
    print("2. Test queries: 'list issues reported on Curana', 'what is the latest on Tithely'")
    print("3. Verify entity focus, time range badges, and clean formatting")


if __name__ == "__main__":
    main()
