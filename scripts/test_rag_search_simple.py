#!/usr/bin/env python3
"""
Simple RAG Search Test Script

This script provides a minimal test of the RAG search functionality
to validate that the system is working correctly after fixes.
"""

import os
import sys
import django
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

# Load environment variables
load_dotenv(os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag', '.env'))

# Setup Django
os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings.production'
django.setup()

from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant, User

def main():
    """Test RAG search functionality."""
    print("🔍 Testing RAG Search Functionality")
    print("=" * 50)
    
    try:
        # Get tenant and user
        tenant = Tenant.objects.filter(name='Default Tenant').first()
        user = User.objects.filter(email='<EMAIL>').first()
        
        if not tenant or not user:
            print("❌ Could not find tenant or user")
            return
            
        print(f"✅ Using tenant: {tenant.name}")
        print(f"✅ Using user: {user.email}")
        
        # Initialize RAG service
        print("\n🤖 Initializing RAG service...")
        rag_service = RAGService(user=user, tenant_slug=tenant.slug)
        print("✅ RAG service initialized")
        
        # Test search
        print("\n🔍 Testing search...")
        query = "What are the main features of the system?"
        search_result, documents = rag_service.search(query, top_k=3)

        print(f"✅ Search completed")
        print(f"📄 Documents found: {len(documents)}")
        print(f"📝 Response length: {len(search_result.generated_answer)} characters")
        print(f"🔍 Query: {query}")
        print(f"📋 Response preview: {search_result.generated_answer[:200]}...")
        
        print("\n🎉 RAG search test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during RAG search test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
