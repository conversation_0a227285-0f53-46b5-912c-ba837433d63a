#!/usr/bin/env python3
"""
Complete Multi-Source Data Ingestion Script

This script ingests data from all configured sources:
- GitHub repositories (Yellowstone, Yosemite)
- Slack channels (if configured)

Usage:
    python scripts/ingest_all_sources.py [--clean] [--github-only] [--slack-only]

Options:
    --clean         Clean existing data before ingestion
    --github-only   Ingest only GitHub data
    --slack-only    Ingest only Slack data
    --days          Number of days to fetch (default: 365)
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime

def main():
    """Main function."""
    print("🚀 Starting Complete Multi-Source Data Ingestion")
    print("=" * 70)
    
    # Parse arguments
    parser = argparse.ArgumentParser(description='Complete Multi-Source Data Ingestion Script')
    parser.add_argument('--clean', action='store_true', help='Clean existing data before ingestion')
    parser.add_argument('--github-only', action='store_true', help='Ingest only GitHub data')
    parser.add_argument('--slack-only', action='store_true', help='Ingest only Slack data')
    parser.add_argument('--days', type=int, default=365, help='Number of days to fetch')

    args = parser.parse_args()
    
    start_time = datetime.now()
    total_processed = 0
    total_failed = 0
    
    # GitHub repositories to ingest
    github_repos = [
        "Compiify/Yellowstone",
        "Compiify/Yosemite"
    ]
    
    # GitHub content types to fetch
    github_content_types = "pull_request,issue,release,wiki"
    
    if not args.slack_only:
        print("\n🐙 GITHUB DATA INGESTION")
        print("=" * 50)
        
        for repo in github_repos:
            print(f"\n📂 Processing repository: {repo}")
            
            # Build command
            cmd = [
                "python", "scripts/ingest_github_production.py",
                "--repo", repo,
                "--days", str(args.days),
                "--content-types", github_content_types
            ]
            
            if args.clean:
                cmd.append("--clean")
            
            try:
                # Run GitHub ingestion
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
                
                if result.returncode == 0:
                    print(f"✅ Successfully ingested data from {repo}")
                    # Parse output to get stats
                    output_lines = result.stdout.split('\n')
                    for line in output_lines:
                        if "Total Processed:" in line:
                            processed = int(line.split(":")[1].strip().split()[0])
                            total_processed += processed
                        elif "Total Failed:" in line:
                            failed = int(line.split(":")[1].strip().split()[0])
                            total_failed += failed
                else:
                    print(f"❌ Failed to ingest data from {repo}")
                    print(f"Error: {result.stderr}")
                    total_failed += 1
                    
            except Exception as e:
                print(f"❌ Error running ingestion for {repo}: {str(e)}")
                total_failed += 1
    
    if not args.github_only:
        print("\n📱 SLACK DATA INGESTION")
        print("=" * 50)
        
        # Check if Slack sources exist and ingest
        try:
            # Try to run existing Slack ingestion if available
            slack_cmd = ["python", "scripts/ingest_production_data.py", "--slack"]
            if args.clean:
                slack_cmd.append("--clean")
                
            result = subprocess.run(slack_cmd, capture_output=True, text=True, cwd=os.getcwd())
            
            if result.returncode == 0:
                print("✅ Successfully ingested Slack data")
                # Parse Slack stats if available
                output_lines = result.stdout.split('\n')
                for line in output_lines:
                    if "Total Processed:" in line:
                        processed = int(line.split(":")[1].strip().split()[0])
                        total_processed += processed
                    elif "Total Failed:" in line:
                        failed = int(line.split(":")[1].strip().split()[0])
                        total_failed += failed
            else:
                print("⚠️  Slack ingestion not available or failed")
                print("   This is normal if no Slack sources are configured")
                
        except Exception as e:
            print(f"⚠️  Slack ingestion not available: {str(e)}")
            print("   This is normal if no Slack sources are configured")
    
    # Generate final report
    end_time = datetime.now()
    duration = end_time - start_time
    
    print("\n" + "=" * 70)
    print("📊 COMPLETE INGESTION REPORT")
    print("=" * 70)
    print(f"⏱️  Total Duration: {duration}")
    print(f"✅ Total Documents Processed: {total_processed}")
    print(f"❌ Total Documents Failed: {total_failed}")
    
    if total_processed > 0:
        success_rate = (total_processed / (total_processed + total_failed)) * 100
        print(f"📈 Overall Success Rate: {success_rate:.1f}%")
    
    print(f"\n🎯 Data Sources Processed:")
    if not args.slack_only:
        print(f"   🐙 GitHub Repositories: {len(github_repos)}")
    if not args.github_only:
        print(f"   📱 Slack Channels: Available if configured")
    
    print(f"\n📋 Next Steps:")
    print(f"   1. Run validation: python scripts/validate_ingestion.py")
    print(f"   2. Test search: python scripts/test_search.py")
    print(f"   3. Check UI: Visit http://localhost:8000")
    
    print("\n🎉 Complete multi-source ingestion finished!")
    
    return total_processed > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
