#!/usr/bin/env python3
"""
Production Data Ingestion Script

This script ingests Slack and GitHub data using the exact same services
that run in production. No intermediary scripts or workarounds.

Usage:
    python scripts/ingest_production_data.py [--slack] [--github] [--clean]

Options:
    --slack     Ingest Slack data
    --github    Ingest GitHub data
    --clean     Clean existing data before ingestion
    --all       Ingest all sources (default if no specific source specified)
"""

import os
import sys
import argparse
from datetime import datetime

def main():
    """Main function."""
    print("🚀 Starting Production Data Ingestion Script")
    sys.stdout.flush()

    # Add the project root to Python path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

    print("📦 Setting up Django...")
    sys.stdout.flush()
    import django
    django.setup()

    # Initialize embedding model BEFORE importing services
    print("🔧 Initializing embedding model...")
    sys.stdout.flush()
    from apps.core.utils.embedding_consistency import set_global_embedding_model
    set_global_embedding_model()

    print("📚 Importing services...")
    sys.stdout.flush()
    from django.contrib.auth.models import User
    from apps.accounts.models import Tenant
    from apps.documents.models import DocumentSource, RawDocument
    from apps.documents.services.ingestion_service import IngestionService

    # Parse arguments
    parser = argparse.ArgumentParser(description='Production Data Ingestion Script')
    parser.add_argument('--slack', action='store_true', help='Ingest Slack data')
    parser.add_argument('--github', action='store_true', help='Ingest GitHub data')
    parser.add_argument('--clean', action='store_true', help='Clean existing data before ingestion')
    parser.add_argument('--all', action='store_true', help='Ingest all sources')

    args = parser.parse_args()
    print(f"📋 Arguments: slack={args.slack}, github={args.github}, clean={args.clean}, all={args.all}")
    sys.stdout.flush()

    # Default to all if no specific source specified
    if not args.slack and not args.github and not args.all:
        args.all = True

    if args.all:
        args.slack = True
        args.github = True

    print(f"📋 Final settings: slack={args.slack}, github={args.github}, clean={args.clean}")
    sys.stdout.flush()

    # Run ingestion
    ingester = ProductionDataIngester(User, Tenant, DocumentSource, RawDocument, IngestionService)
    success = ingester.run(
        ingest_slack=args.slack,
        ingest_github=args.github,
        clean_data=args.clean
    )

    sys.exit(0 if success else 1)


class ProductionDataIngester:
    """Production-ready data ingestion using actual production services."""

    def __init__(self, User, Tenant, DocumentSource, RawDocument, IngestionService):
        self.User = User
        self.Tenant = Tenant
        self.DocumentSource = DocumentSource
        self.RawDocument = RawDocument
        self.IngestionService = IngestionService

        self.tenant = None
        self.user = None
        self.ingestion_service = None
        self.stats = {
            "total_processed": 0,
            "total_failed": 0,
            "sources_processed": 0,
            "start_time": datetime.now()
        }
    
    def setup_environment(self):
        """Setup tenant, user, and ingestion service."""
        print("🔧 Setting up production environment...")

        # Get tenant and user (using the same ones as in production)
        try:
            self.tenant = self.Tenant.objects.get(slug='default')
            self.user = self.User.objects.get(username='mahesh')
            print(f"✅ Using tenant: {self.tenant.name}")
            print(f"✅ Using user: {self.user.username}")
        except (self.Tenant.DoesNotExist, self.User.DoesNotExist) as e:
            print(f"❌ Error: {str(e)}")
            print("Please ensure tenant 'default' and user 'mahesh' exist.")
            return False

        # Initialize ingestion service (exactly as used in production)
        self.ingestion_service = self.IngestionService(self.tenant, self.user)
        print("✅ Production ingestion service initialized")

        return True
    
    def clean_existing_data(self, source_types=None):
        """Clean existing data for specified source types."""
        print("🧹 Cleaning existing data...")
        
        if source_types:
            sources = self.DocumentSource.objects.filter(
                tenant=self.tenant,
                source_type__in=source_types
            )
        else:
            sources = self.DocumentSource.objects.filter(tenant=self.tenant)

        total_deleted = 0
        for source in sources:
            count = self.RawDocument.objects.filter(source=source).count()
            if count > 0:
                self.RawDocument.objects.filter(source=source).delete()
                print(f"  🗑️  Deleted {count} documents from {source.name}")
                total_deleted += count
        
        print(f"✅ Cleaned {total_deleted} total documents")
    
    def ingest_slack_data(self):
        """Ingest Slack data using production service."""
        print("\n📱 Ingesting Slack Data")
        print("=" * 50)
        
        # Get Slack sources
        slack_sources = self.DocumentSource.objects.filter(
            tenant=self.tenant,
            source_type__in=['slack', 'local_slack'],
            is_active=True
        )
        
        if not slack_sources.exists():
            print("⚠️  No active Slack sources found")
            return 0, 0
        
        total_processed = 0
        total_failed = 0
        
        for source in slack_sources:
            print(f"\n🔄 Processing: {source.name}")
            print(f"   Type: {source.source_type}")
            print(f"   Config: {source.config}")
            
            try:
                # Create processing job (exactly as in production)
                job = self.ingestion_service.create_processing_job(source)
                print(f"   📋 Created processing job: {job.id}")
                
                # Process source with production parameters
                if source.source_type == 'local_slack':
                    # Local Slack processing
                    processed, failed = self.ingestion_service.process_source(
                        source=source,
                        job=job,
                        batch_size=20,
                        data_dir=source.config.get('data_dir', '../data/'),
                        channel_id=source.config.get('channel_id'),
                        days=source.config.get('custom_days', 30)
                    )
                else:
                    # API Slack processing
                    processed, failed = self.ingestion_service.process_source(
                        source=source,
                        job=job,
                        batch_size=20,
                        days_back=30,
                        include_threads=True,
                        filter_bots=True
                    )
                
                print(f"   ✅ Processed: {processed} documents")
                print(f"   ❌ Failed: {failed} documents")
                
                total_processed += processed
                total_failed += failed
                self.stats["sources_processed"] += 1
                
            except Exception as e:
                print(f"   ❌ Error processing {source.name}: {str(e)}")
                total_failed += 1
        
        return total_processed, total_failed
    
    def ingest_github_data(self):
        """Ingest GitHub data using production service."""
        print("\n🐙 Ingesting GitHub Data")
        print("=" * 50)
        
        # Get GitHub sources
        github_sources = self.DocumentSource.objects.filter(
            tenant=self.tenant,
            source_type='github',
            is_active=True
        )
        
        if not github_sources.exists():
            print("⚠️  No active GitHub sources found")
            return 0, 0
        
        total_processed = 0
        total_failed = 0
        
        for source in github_sources:
            print(f"\n🔄 Processing: {source.name}")
            print(f"   Repository: {source.config.get('owner')}/{source.config.get('repo')}")
            print(f"   Config: {source.config}")
            
            try:
                # Create processing job (exactly as in production)
                job = self.ingestion_service.create_processing_job(source)
                print(f"   📋 Created processing job: {job.id}")
                
                # Process source with production parameters
                processed, failed = self.ingestion_service.process_source(
                    source=source,
                    job=job,
                    batch_size=10,
                    days=365,  # Last 1 year as configured
                    state="all",
                    include_drafts=True,
                    max_per_type=50,
                    content_types=['pull_request', 'issue', 'wiki', 'release', 'discussion']
                )
                
                print(f"   ✅ Processed: {processed} documents")
                print(f"   ❌ Failed: {failed} documents")
                
                total_processed += processed
                total_failed += failed
                self.stats["sources_processed"] += 1
                
            except Exception as e:
                print(f"   ❌ Error processing {source.name}: {str(e)}")
                total_failed += 1
        
        return total_processed, total_failed
    
    def generate_report(self):
        """Generate ingestion report."""
        end_time = datetime.now()
        duration = end_time - self.stats["start_time"]
        
        print("\n" + "=" * 60)
        print("📊 INGESTION REPORT")
        print("=" * 60)
        print(f"⏱️  Duration: {duration}")
        print(f"📁 Sources Processed: {self.stats['sources_processed']}")
        print(f"✅ Total Processed: {self.stats['total_processed']} documents")
        print(f"❌ Total Failed: {self.stats['total_failed']} documents")
        
        if self.stats['total_processed'] > 0:
            success_rate = (self.stats['total_processed'] / 
                          (self.stats['total_processed'] + self.stats['total_failed'])) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")
        
        print("\n🎉 Ingestion completed!")
    
    def run(self, ingest_slack=False, ingest_github=False, clean_data=False):
        """Run the ingestion process."""
        print("🚀 Starting Production Data Ingestion")
        print("=" * 60)
        
        # Setup environment
        if not self.setup_environment():
            return False
        
        # Clean data if requested
        if clean_data:
            source_types = []
            if ingest_slack:
                source_types.extend(['slack', 'local_slack'])
            if ingest_github:
                source_types.append('github')
            self.clean_existing_data(source_types if source_types else None)
        
        # Ingest data
        if ingest_slack:
            processed, failed = self.ingest_slack_data()
            self.stats["total_processed"] += processed
            self.stats["total_failed"] += failed
        
        if ingest_github:
            processed, failed = self.ingest_github_data()
            self.stats["total_processed"] += processed
            self.stats["total_failed"] += failed
        
        # Generate report
        self.generate_report()
        
        return True


if __name__ == "__main__":
    main()
