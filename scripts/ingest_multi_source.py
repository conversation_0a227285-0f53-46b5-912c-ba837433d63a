#!/usr/bin/env python3
"""
Multi-source ingestion script.

This script ingests data from both Slack and GitHub sources with proper
configuration and error handling.
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.accounts.models import Tenant, User
from apps.documents.models import DocumentSource
from apps.documents.services.ingestion_service import IngestionService

def setup_slack_source(tenant):
    """Set up Slack data source."""
    print("📱 Setting up Slack source...")
    
    source, created = DocumentSource.objects.get_or_create(
        tenant=tenant,
        name="Local Slack Data",
        source_type="local_slack",
        defaults={
            "config": {
                "data_dir": "../data/",
                "channel_id": "1-productengineering",
                "max_documents_per_channel": 100,
                "custom_days": 30
            },
            "is_active": True
        }
    )
    
    if created:
        print(f"✅ Created new Slack source: {source.name}")
    else:
        print(f"✅ Using existing Slack source: {source.name}")
    
    return source

def setup_github_sources(tenant):
    """Set up GitHub data sources."""
    print("🐙 Setting up GitHub sources...")
    
    # Yellowstone repository
    yellowstone_source, created = DocumentSource.objects.get_or_create(
        tenant=tenant,
        name="Yellowstone GitHub",
        source_type="github",
        defaults={
            "config": {
                "token": "****************************************",
                "owner": "Compiify",
                "repo": "Yellowstone",
                "content_types": ["pull_request", "issue", "release", "wiki"]
            },
            "is_active": True
        }
    )
    
    if created:
        print(f"✅ Created new GitHub source: {yellowstone_source.name}")
    else:
        print(f"✅ Using existing GitHub source: {yellowstone_source.name}")
    
    # Yosemite repository
    yosemite_source, created = DocumentSource.objects.get_or_create(
        tenant=tenant,
        name="Yosemite GitHub",
        source_type="github",
        defaults={
            "config": {
                "token": "****************************************",
                "owner": "Compiify",
                "repo": "Yosemite",
                "content_types": ["pull_request", "issue", "release", "wiki"]
            },
            "is_active": True
        }
    )
    
    if created:
        print(f"✅ Created new GitHub source: {yosemite_source.name}")
    else:
        print(f"✅ Using existing GitHub source: {yosemite_source.name}")
    
    return [yellowstone_source, yosemite_source]

def ingest_source(ingestion_service, source, source_type):
    """Ingest data from a single source."""
    print(f"\n📥 Ingesting {source_type} data from: {source.name}")
    print("-" * 50)
    
    try:
        # Configure ingestion parameters based on source type
        if source_type == "slack":
            kwargs = {
                "batch_size": 20,
                "days_back": 30,
                "include_threads": True,
                "filter_bots": True
            }
        elif source_type == "github":
            kwargs = {
                "batch_size": 10,
                "days": 30,
                "state": "all",
                "include_drafts": True,
                "max_per_type": 20
            }
        else:
            kwargs = {"batch_size": 10}
        
        # Process source
        processed, failed = ingestion_service.process_source(
            source=source,
            **kwargs
        )
        
        print(f"✅ {source_type.title()} ingestion completed!")
        print(f"   Processed: {processed} documents")
        print(f"   Failed: {failed} documents")
        
        return processed, failed
        
    except Exception as e:
        print(f"❌ Error ingesting {source_type} data: {str(e)}")
        return 0, 0

def main():
    """Main ingestion function."""
    print("🚀 Multi-Source Data Ingestion")
    print("=" * 60)
    
    try:
        # Get tenant and user
        tenant = Tenant.objects.first()
        user = User.objects.first()
        
        if not tenant:
            print("❌ No tenant found. Please create a tenant first.")
            return
        
        if not user:
            print("❌ No user found. Please create a user first.")
            return
        
        print(f"✅ Using tenant: {tenant.name}")
        print(f"✅ Using user: {user.email}")
        
        # Create ingestion service
        ingestion_service = IngestionService(tenant, user)
        print("✅ Ingestion service initialized")
        
        # Set up sources
        slack_source = setup_slack_source(tenant)
        github_sources = setup_github_sources(tenant)
        
        # Track totals
        total_processed = 0
        total_failed = 0
        
        # Ingest Slack data
        processed, failed = ingest_source(ingestion_service, slack_source, "slack")
        total_processed += processed
        total_failed += failed
        
        # Ingest GitHub data
        for github_source in github_sources:
            processed, failed = ingest_source(ingestion_service, github_source, "github")
            total_processed += processed
            total_failed += failed
        
        # Summary
        print(f"\n🎉 Multi-source ingestion completed!")
        print(f"=" * 60)
        print(f"📊 Total Results:")
        print(f"   Sources processed: {1 + len(github_sources)}")
        print(f"   Documents processed: {total_processed}")
        print(f"   Documents failed: {total_failed}")
        print(f"   Success rate: {(total_processed / (total_processed + total_failed) * 100):.1f}%" if (total_processed + total_failed) > 0 else "N/A")
        
        if total_processed > 0:
            print(f"\n✅ Data ingestion successful! You can now test RAG search.")
        else:
            print(f"\n⚠️  No documents were processed. Please check the configuration.")
        
    except Exception as e:
        print(f"❌ Error in multi-source ingestion: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
