#!/usr/bin/env python3
"""
Ingestion Validation Script

This script validates the ingested data quality and completeness.
It checks:
- Data integrity
- Content quality
- Source distribution
- Metadata completeness
- Search readiness

Usage:
    python scripts/validate_ingestion.py [--detailed]
"""

import os
import sys
import argparse
from datetime import datetime, timedelta

def main():
    """Main function."""
    print("🔍 Starting Ingestion Validation")
    print("=" * 50)

    # Add the project root to Python path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

    import django
    django.setup()

    # Parse arguments
    parser = argparse.ArgumentParser(description='Ingestion Validation Script')
    parser.add_argument('--detailed', action='store_true', help='Show detailed validation results')
    args = parser.parse_args()

    # Run validation
    validator = IngestionValidator(detailed=args.detailed)
    success = validator.run_validation()

    sys.exit(0 if success else 1)


class IngestionValidator:
    """Validates ingested data quality and completeness."""

    def __init__(self, detailed: bool = False):
        self.detailed = detailed
        self.issues = []
        self.stats = {}

    def run_validation(self):
        """Run complete validation suite."""
        print("🔍 Running Complete Ingestion Validation")
        print("=" * 60)

        # Import models
        from apps.accounts.models import Tenant
        from apps.documents.models import DocumentSource, RawDocument, DocumentContent

        # Get tenant
        try:
            tenant = Tenant.objects.get(slug='default')
            print(f"✅ Using tenant: {tenant.name}")
        except Tenant.DoesNotExist:
            print("❌ Default tenant not found")
            return False

        # Validate data counts
        self.validate_data_counts(tenant, DocumentSource, RawDocument, DocumentContent)
        
        # Validate data quality
        self.validate_data_quality(tenant, RawDocument, DocumentContent)
        
        # Validate source distribution
        self.validate_source_distribution(tenant, DocumentSource, RawDocument)
        
        # Validate metadata completeness
        self.validate_metadata_completeness(tenant, RawDocument)
        
        # Validate content quality
        self.validate_content_quality(tenant, RawDocument, DocumentContent)
        
        # Generate report
        self.generate_validation_report()
        
        return len(self.issues) == 0

    def validate_data_counts(self, tenant, DocumentSource, RawDocument, DocumentContent):
        """Validate basic data counts."""
        print("\n📊 Validating Data Counts")
        print("-" * 30)

        # Count sources
        sources = DocumentSource.objects.filter(tenant=tenant)
        source_count = sources.count()
        print(f"📁 Document Sources: {source_count}")

        if source_count == 0:
            self.issues.append("No document sources found")
            return

        # Count documents
        documents = RawDocument.objects.filter(tenant=tenant)
        doc_count = documents.count()
        print(f"📄 Raw Documents: {doc_count}")

        if doc_count == 0:
            self.issues.append("No documents found")
            return

        # Count content
        content_count = DocumentContent.objects.filter(document__tenant=tenant).count()
        print(f"📝 Document Content: {content_count}")

        # Calculate coverage
        content_coverage = (content_count / doc_count * 100) if doc_count > 0 else 0
        print(f"📈 Content Coverage: {content_coverage:.1f}%")

        if content_coverage < 95:
            self.issues.append(f"Low content coverage: {content_coverage:.1f}%")

        self.stats.update({
            'sources': source_count,
            'documents': doc_count,
            'content': content_count,
            'content_coverage': content_coverage
        })

    def validate_data_quality(self, tenant, RawDocument, DocumentContent):
        """Validate data quality metrics."""
        print("\n🎯 Validating Data Quality")
        print("-" * 30)

        documents = RawDocument.objects.filter(tenant=tenant)
        
        # Check for empty titles
        empty_titles = documents.filter(title__isnull=True).count()
        empty_titles += documents.filter(title__exact='').count()
        print(f"📝 Documents with empty titles: {empty_titles}")
        
        if empty_titles > 0:
            self.issues.append(f"{empty_titles} documents have empty titles")

        # Check for missing external IDs
        missing_external_ids = documents.filter(external_id__isnull=True).count()
        missing_external_ids += documents.filter(external_id__exact='').count()
        print(f"🔗 Documents missing external IDs: {missing_external_ids}")
        
        if missing_external_ids > 0:
            self.issues.append(f"{missing_external_ids} documents missing external IDs")

        # Check content sizes
        from django.db import models
        content_objects = DocumentContent.objects.filter(document__tenant=tenant)
        avg_content_size = content_objects.aggregate(avg_size=models.Avg('content_size'))['avg_size'] or 0
        print(f"📏 Average content size: {avg_content_size:.0f} characters")

        # Check for very small content
        small_content = content_objects.filter(content_size__lt=50).count()
        print(f"📉 Documents with small content (<50 chars): {small_content}")
        
        if small_content > (content_objects.count() * 0.1):  # More than 10%
            self.issues.append(f"Too many documents with small content: {small_content}")

    def validate_source_distribution(self, tenant, DocumentSource, RawDocument):
        """Validate source distribution."""
        print("\n🌐 Validating Source Distribution")
        print("-" * 30)

        sources = DocumentSource.objects.filter(tenant=tenant)
        
        for source in sources:
            doc_count = RawDocument.objects.filter(source=source).count()
            print(f"📂 {source.name} ({source.source_type}): {doc_count} documents")
            
            if doc_count == 0:
                self.issues.append(f"Source '{source.name}' has no documents")

        # Check for GitHub sources
        github_sources = sources.filter(source_type='github').count()
        if github_sources == 0:
            self.issues.append("No GitHub sources found")

        print(f"🐙 GitHub sources: {github_sources}")

    def validate_metadata_completeness(self, tenant, RawDocument):
        """Validate metadata completeness."""
        print("\n🏷️  Validating Metadata Completeness")
        print("-" * 30)

        documents = RawDocument.objects.filter(tenant=tenant)
        
        # Check for missing metadata
        missing_metadata = documents.filter(metadata__isnull=True).count()
        empty_metadata = documents.filter(metadata__exact={}).count()
        print(f"📋 Documents missing metadata: {missing_metadata}")
        print(f"📋 Documents with empty metadata: {empty_metadata}")
        
        if missing_metadata > 0:
            self.issues.append(f"{missing_metadata} documents missing metadata")

        # Check for missing permalinks
        missing_permalinks = documents.filter(permalink__isnull=True).count()
        missing_permalinks += documents.filter(permalink__exact='').count()
        print(f"🔗 Documents missing permalinks: {missing_permalinks}")
        
        if missing_permalinks > (documents.count() * 0.1):  # More than 10%
            self.issues.append(f"Too many documents missing permalinks: {missing_permalinks}")

    def validate_content_quality(self, tenant, RawDocument, DocumentContent):
        """Validate content quality."""
        print("\n📝 Validating Content Quality")
        print("-" * 30)

        # Check recent documents
        recent_cutoff = datetime.now() - timedelta(days=30)
        recent_docs = RawDocument.objects.filter(
            tenant=tenant,
            created_at__gte=recent_cutoff
        ).count()
        print(f"📅 Recent documents (last 30 days): {recent_docs}")

        # Check content types
        content_types = RawDocument.objects.filter(tenant=tenant).values_list(
            'content_type', flat=True
        ).distinct()
        print(f"📑 Content types found: {list(content_types)}")

        # Check for GitHub-specific content
        github_docs = RawDocument.objects.filter(
            tenant=tenant,
            content_type__startswith='github'
        ).count()
        print(f"🐙 GitHub documents: {github_docs}")

        if github_docs == 0:
            self.issues.append("No GitHub documents found")

    def generate_validation_report(self):
        """Generate final validation report."""
        print("\n" + "=" * 60)
        print("📊 INGESTION VALIDATION REPORT")
        print("=" * 60)

        # Summary stats
        print(f"📁 Total Sources: {self.stats.get('sources', 0)}")
        print(f"📄 Total Documents: {self.stats.get('documents', 0)}")
        print(f"📝 Content Coverage: {self.stats.get('content_coverage', 0):.1f}%")

        # Issues found
        if self.issues:
            print(f"\n⚠️  Issues Found ({len(self.issues)}):")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        else:
            print(f"\n✅ No issues found - data quality is excellent!")

        # Recommendations
        print(f"\n💡 Recommendations:")
        if self.stats.get('documents', 0) < 10:
            print(f"   • Consider ingesting more data for better search quality")
        if self.stats.get('content_coverage', 0) < 100:
            print(f"   • Some documents are missing content - check ingestion process")
        if len(self.issues) > 0:
            print(f"   • Address the issues listed above before proceeding")
        else:
            print(f"   • Data is ready for search and RAG operations")
            print(f"   • Consider running end-to-end search tests")

        print(f"\n🎯 Next Steps:")
        print(f"   1. Test search functionality: python scripts/test_search.py")
        print(f"   2. Validate UI: python scripts/test_ui_end_to_end.py")
        print(f"   3. Check system health: python scripts/validate_data_consistency.py")

        if len(self.issues) == 0:
            print(f"\n🎉 Validation completed successfully!")
        else:
            print(f"\n⚠️  Validation completed with {len(self.issues)} issues")


if __name__ == "__main__":
    main()
