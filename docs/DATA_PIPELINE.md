# Data Pipeline Guide

Comprehensive guide to the RAGSearch data ingestion, processing, and validation pipeline.

## 🔄 Pipeline Overview

The RAGSearch data pipeline consists of three main stages:
1. **🧹 Cleanup**: Remove existing data safely
2. **📥 Ingestion**: Process and store new data
3. **🔍 Validation**: Ensure data consistency and integrity

## 🚀 Quick Start

### Complete Pipeline (Recommended)
```bash
cd multi_source_rag
python ../scripts/data_pipeline.py
```

This runs all three stages automatically with proper error handling and reporting.

### Individual Stages
```bash
# Stage 1: Cleanup existing data
python ../scripts/cleanup_data.py

# Stage 2: Ingest fresh data
python ../scripts/ingest_data.py

# Stage 3: Validate data consistency
python ../scripts/validate_data_consistency.py
```

## 🧹 Stage 1: Data Cleanup

### Purpose
Safely removes all existing data while preserving:
- Database schema and structure
- User accounts and authentication
- Document source configurations
- System settings

### What Gets Cleaned
- ✅ All search results and queries
- ✅ All document chunks and embeddings
- ✅ All vector data in Qdrant
- ✅ All citation records
- ❌ User accounts (preserved)
- ❌ Document sources (preserved)
- ❌ System configuration (preserved)

### Safety Features
- Interactive confirmation required
- Comprehensive verification step
- Detailed logging of all operations
- Rollback capability for critical errors

### Usage
```bash
python ../scripts/cleanup_data.py

# Expected output:
# 🧹 COMPREHENSIVE DATA CLEANUP
# ⚠️  WARNING: This will delete ALL data!
# Are you sure you want to proceed? Type 'YES' to confirm: YES
# 
# 🚀 Starting cleanup...
# ✅ Deleted 1/1 collections
# ✅ Deleted 545 documents
# ✅ Deleted 1,200 chunks
# 🎉 CLEANUP COMPLETE!
```

## 📥 Stage 2: Data Ingestion

### Purpose
Processes documents from configured sources using production-grade services and stores them in both Django database and Qdrant vector database.

### Supported Data Sources
- **Slack**: Team conversations with thread context
- **GitHub**: Repositories, issues, PRs, discussions
- **Local Files**: JSON data dumps
- **Future**: Confluence, Google Docs, Jira

### Ingestion Process

**1. Source Selection**
```bash
python ../scripts/ingest_data.py

# Interactive menu:
# 🎯 INGESTION OPTIONS:
#    1. Ingest all sources
#    2. Ingest specific source
#    3. Exit
```

**2. Processing Pipeline**
- Document retrieval from sources
- Content analysis and classification
- Intelligent chunking strategies
- Embedding generation (BGE-base-en-v1.5)
- Vector storage in Qdrant
- Metadata storage in Django
- Relationship mapping and indexing

**3. Chunking Strategies**

**Skip Chunking** (Slack messages)
- Pre-optimized content that doesn't need chunking
- Preserves message integrity and context
- Maintains thread relationships

**Token-Based Chunking** (Large documents)
- 500-token chunks with 50-token overlap
- Optimized for LLM context windows
- Preserves semantic boundaries

**Semantic Chunking** (Technical documents)
- Content-aware splitting using LlamaIndex
- Maintains logical document structure
- Optimizes for retrieval quality

### Production Features
- **Error Recovery**: Automatic fallback and retry mechanisms
- **Data Consistency**: Synchronized Django and Qdrant operations
- **Progress Tracking**: Real-time statistics and timing
- **Job Management**: Proper DocumentProcessingJob records
- **Batch Processing**: Efficient handling of large datasets

### Usage Example
```bash
python ../scripts/ingest_data.py

# Expected output:
# 🚀 PRODUCTION-GRADE DATA INGESTION
# ✅ Using tenant: Stride (stride)
# 
# 📚 AVAILABLE DOCUMENT SOURCES:
#    1. Team Slack (slack)
#    2. Main Repository (github)
# 
# 🚀 INGESTING SOURCE: Team Slack
# ✅ Created processing job: 123
# ✅ Processing completed!
#    Processed: 545 documents
#    Failed: 0 documents
#    Time: 45.2 seconds
#    Rate: 12.1 docs/sec
#    Chunks created: 1,200
```

## 🔍 Stage 3: Data Validation

### Purpose
Comprehensive validation of data integrity and consistency between Django database and Qdrant vector database.

### Validation Checks

**1. Django Database Health**
- Record counts and relationships
- Orphaned record detection
- Data integrity constraints
- Index performance

**2. Qdrant Database Health**
- Collection status and connectivity
- Vector count and dimensions
- Index optimization status
- Performance metrics

**3. Cross-Database Consistency**
- Vector ID mapping validation
- Embedding metadata synchronization
- Count consistency between systems
- Sample data verification

**4. Source-Specific Validation**
- Per-source consistency checks
- Chunking strategy verification
- Relationship integrity
- Quality scoring

### Validation Reports

**Console Output**
```bash
python ../scripts/validate_data_consistency.py

# Expected output:
# 🔍 PRODUCTION-GRADE DATA CONSISTENCY VALIDATION
# 
# 🔍 VALIDATING DJANGO DATABASE
# 📊 Database counts:
#    document_chunks: 1,200
#    embedding_metadata: 1,200
#    search_results: 0
# 
# 🔗 Orphaned records:
#    ✅ orphaned_chunks: 0
#    ✅ orphaned_embeddings: 0
# 
# 📈 Embedding coverage: 100.0%
# 
# 🔍 VALIDATING QDRANT DATABASE
# ✅ Connected to Qdrant: http://localhost:6333
# 📊 Collections found: 1
#    ✅ tenant_stride_default: 1,200 vectors
# 
# 🔍 VALIDATING CROSS-DATABASE CONSISTENCY
# 📊 Django embedding metadata: 1,200
# 📊 Qdrant total vectors: 1,200
# 📈 Consistency ratio: 1.000
# 🎯 Status: ✅ Consistent
# 
# 🏆 OVERALL STATUS: ✅ HEALTHY
```

**JSON Reports**
Detailed reports saved as timestamped JSON files:
```json
{
  "timestamp": "2024-06-03T10:30:00",
  "validation_results": {
    "django": {
      "is_healthy": true,
      "counts": {...},
      "orphans": {...}
    },
    "qdrant": {
      "is_healthy": true,
      "collections": [...],
      "total_vectors": 1200
    },
    "cross_db": {
      "is_consistent": true,
      "consistency_ratio": 1.0
    }
  },
  "summary": {
    "overall_healthy": true
  }
}
```

## 🔧 Advanced Configuration

### Environment Variables
```bash
# Ingestion settings
BATCH_SIZE=50
MAX_RETRIES=3
CHUNK_SIZE=500
CHUNK_OVERLAP=50

# Validation settings
CONSISTENCY_THRESHOLD=0.95
SAMPLE_SIZE=100
VALIDATION_TIMEOUT=300
```

### Custom Data Sources

**1. Create DocumentSource**
```python
# In Django admin or shell
from apps.documents.models import DocumentSource

source = DocumentSource.objects.create(
    tenant=tenant,
    name="Custom Source",
    source_type="custom",
    config={
        "endpoint": "https://api.example.com",
        "format": "json",
        "batch_size": 100
    },
    credentials={
        "api_key": "your-api-key"
    }
)
```

**2. Implement Custom Processor**
```python
# In apps/documents/services/processors/
class CustomProcessor:
    def process_documents(self, source, documents):
        # Custom processing logic
        pass
```

### Performance Tuning

**1. Batch Size Optimization**
```bash
# For large datasets
export INGESTION_BATCH_SIZE=100

# For memory-constrained environments
export INGESTION_BATCH_SIZE=25
```

**2. Parallel Processing**
```bash
# Enable parallel ingestion (future feature)
export ENABLE_PARALLEL_INGESTION=true
export MAX_WORKERS=4
```

## 🚨 Troubleshooting

### Common Issues

**1. Ingestion Failures**
```bash
# Symptoms: Documents fail to process
# Check: Source configuration and credentials
# Solution: Verify API tokens and permissions

# Debug specific source
python manage.py shell
from apps.documents.services.ingestion_service import IngestionService
service = IngestionService(tenant=tenant)
service.test_source_connection(source)
```

**2. Consistency Issues**
```bash
# Symptoms: Vector count mismatch
# Check: Embedding metadata records
# Solution: Re-run ingestion with cleanup

python ../scripts/cleanup_data.py
python ../scripts/ingest_data.py
```

**3. Performance Issues**
```bash
# Symptoms: Slow ingestion or validation
# Check: Database and Qdrant performance
# Solution: Optimize batch sizes and indexes

# Monitor during ingestion
htop
docker stats qdrant
```

### Recovery Procedures

**1. Partial Failure Recovery**
```bash
# If ingestion partially fails
python ../scripts/validate_data_consistency.py
# Review failed documents in processing job logs
# Re-run ingestion for specific sources
```

**2. Complete System Recovery**
```bash
# If system is in inconsistent state
python ../scripts/cleanup_data.py
python ../scripts/ingest_data.py
python ../scripts/validate_data_consistency.py
```

## 📊 Monitoring and Metrics

### Key Metrics
- **Ingestion Rate**: Documents processed per second
- **Success Rate**: Percentage of successful document processing
- **Consistency Score**: Cross-database synchronization percentage
- **Quality Score**: Average confidence of search results

### Monitoring Commands
```bash
# Real-time ingestion monitoring
tail -f logs/ingestion.log

# Performance metrics
python manage.py shell
from apps.search.models import SearchResult
avg_confidence = SearchResult.objects.aggregate(avg_confidence=Avg('llm_confidence_score'))

# System health check
python ../scripts/validate_data_consistency.py --quick
```

For detailed system architecture, see [Architecture Guide](ARCHITECTURE.md).
For administrative tasks, see [Admin Guide](ADMIN_GUIDE.md).
