#!/usr/bin/env python3
"""
Test script to validate the time range feature for document retrieval.

This script tests:
1. Time range calculation from retrieved documents
2. UI display of time range information
3. Different query types and their time ranges

Usage:
    cd /Users/<USER>/Desktop/RAGSearch/multi_source_rag
    python ../scripts/test_time_range_feature.py
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.models import SearchResult
from apps.search.services.rag_service import RAGService
from django.contrib.auth.models import User
from apps.accounts.models import Tenant
import json


def test_time_range_calculation():
    """Test time range calculation for different queries."""
    print("🕒 Testing Time Range Feature...")
    
    # Get tenant and user
    tenant = Tenant.objects.first()
    user = User.objects.first()
    
    if not tenant or not user:
        print("❌ No tenant or user found. Please ensure test data exists.")
        return
    
    rag_service = RAGService(tenant_slug=tenant.slug, user=user)
    
    test_queries = [
        "what is the latest on Tithely",
        "list issues reported on Curana",
        "show me recent problems with Amanda",
        "what happened yesterday"
    ]
    
    for query in test_queries:
        print(f"\n📝 Testing: '{query}'")
        
        try:
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=10,
                min_relevance_score=0.10,
                use_hybrid_search=True,
                use_enhanced_prompts=True
            )
            
            print(f"    ✅ Search completed")
            print(f"    📊 Retrieved {len(retrieved_docs)} documents")
            print(f"    📄 Citations: {search_result.citations.count()}")
            
            # Check time range
            if hasattr(search_result, 'metadata') and search_result.metadata:
                time_range = search_result.metadata.get('time_range')
                if time_range:
                    print(f"    📅 Time Range:")
                    print(f"        Range: {time_range['formatted_range']}")
                    print(f"        Latest: {time_range['relative_latest']}")
                    print(f"        Earliest: {time_range['relative_earliest']}")
                    print(f"        Span: {time_range['days_span']} days")
                    print(f"        Documents: {time_range['document_count']}")
                else:
                    print("    ❌ No time range calculated")
            else:
                print("    ❌ No metadata found")
                
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")


def test_recent_search_results():
    """Test time range information in recent search results."""
    print("\n🔍 Testing Recent Search Results...")
    
    # Get recent search results
    recent_results = SearchResult.objects.filter(
        metadata__isnull=False
    ).order_by('-timestamp')[:5]
    
    if not recent_results:
        print("❌ No recent search results with metadata found")
        return
    
    for result in recent_results:
        print(f"\n📋 Search Result ID: {result.id}")
        print(f"    Query: {result.search_query.query_text[:50]}...")
        print(f"    Timestamp: {result.timestamp}")
        
        if result.metadata and 'time_range' in result.metadata:
            time_range = result.metadata['time_range']
            print(f"    📅 Time Range: {time_range['formatted_range']}")
            print(f"    🕐 Latest: {time_range['relative_latest']}")
            print(f"    📊 Span: {time_range['days_span']} days")
        else:
            print("    ❌ No time range in metadata")


def test_ui_display_format():
    """Test UI display format for time ranges."""
    print("\n🎨 Testing UI Display Formats...")
    
    # Sample time range data
    sample_time_ranges = [
        {
            'formatted_range': 'February 25 - 26, 2025',
            'relative_latest': 'today',
            'relative_earliest': 'yesterday',
            'days_span': 1,
            'document_count': 5
        },
        {
            'formatted_range': 'February 20 - 26, 2025',
            'relative_latest': 'today',
            'relative_earliest': '6 days ago',
            'days_span': 6,
            'document_count': 12
        },
        {
            'formatted_range': 'January 15 - February 26, 2025',
            'relative_latest': 'today',
            'relative_earliest': '1 month ago',
            'days_span': 42,
            'document_count': 25
        }
    ]
    
    for i, time_range in enumerate(sample_time_ranges, 1):
        print(f"\n📱 UI Format Example {i}:")
        print(f"    Badge: 📅 {time_range['relative_latest']}")
        print(f"    Tooltip: Data coverage: {time_range['formatted_range']}")
        print(f"    Sources: Data from {time_range['formatted_range']}")
        if time_range['days_span'] > 0:
            plural = 's' if time_range['days_span'] > 1 else ''
            print(f"    Span: ({time_range['days_span']} day{plural} span)")


def main():
    """Run all time range tests."""
    print("🚀 Time Range Feature Testing")
    print("=" * 50)
    
    test_time_range_calculation()
    test_recent_search_results()
    test_ui_display_format()
    
    print("\n✅ Time Range Feature Testing Completed!")
    print("\nTo test in browser:")
    print("1. Go to http://127.0.0.1:8000/search/")
    print("2. Search: 'what is the latest on Tithely'")
    print("3. Look for time range badges and tooltips")
    print("4. Check sources section for date range information")


if __name__ == "__main__":
    main()
