"""
File interface for document sources.
"""

import hashlib
import mimetypes
import os
from typing import Any, Dict, List, Optional

from .base import DocumentSourceInterface


class FileSourceInterface(DocumentSourceInterface):
    """
    Interface for file document sources.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the file source interface.

        Args:
            config: Configuration for the file source
        """
        super().__init__(config)
        self.base_path = config.get("base_path", "")
        self.file_extensions = config.get(
            "file_extensions", [".txt", ".md", ".pdf", ".html"]
        )

    def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch documents from the file source.

        Args:
            **kwargs: Additional arguments for fetching documents

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        documents = []

        # Get the path to scan
        path = kwargs.get("path", self.base_path)

        # Check if the path exists
        if not os.path.exists(path):
            return documents

        # If the path is a file, process it
        if os.path.isfile(path):
            document = self._process_file(path)
            if document:
                documents.append(document)
            return documents

        # If the path is a directory, process all files in it
        for root, _, files in os.walk(path):
            for file in files:
                file_path = os.path.join(root, file)
                _, ext = os.path.splitext(file_path)

                # Skip files with unsupported extensions
                if ext.lower() not in self.file_extensions:
                    continue

                document = self._process_file(file_path)
                if document:
                    documents.append(document)

        return documents

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get a specific document from the file source.

        Args:
            document_id: ID of the document to get
            **kwargs: Additional arguments for getting the document (unused but kept for interface compatibility)

        Returns:
            Dict[str, Any]: Document
        """
        # In this implementation, document_id is the file path
        file_path = document_id

        # Check if the file exists
        if not os.path.exists(file_path):
            return {}

        # Process the file
        document = self._process_file(file_path)

        return document or {}

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents in the file source.

        Args:
            query: Query to search for (unused in this simple implementation)
            **kwargs: Additional arguments for searching documents

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        # This is a simple implementation that just returns all documents
        # In a real implementation, you would want to use a more sophisticated search
        # The query parameter is unused but kept for interface compatibility
        return self.fetch_documents(**kwargs)

    def _process_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Process a file and return a document.

        Args:
            file_path: Path to the file

        Returns:
            Optional[Dict[str, Any]]: Document
        """
        try:
            # Get file metadata
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            file_type, _ = mimetypes.guess_type(file_path)

            # Read file content
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Generate a unique ID for the document
            document_id = hashlib.md5(file_path.encode()).hexdigest()

            # Determine content type
            _, ext = os.path.splitext(file_path)
            content_type = "text"
            if ext.lower() == ".md":
                content_type = "markdown"
            elif ext.lower() == ".html":
                content_type = "html"
            elif ext.lower() in [".py", ".js", ".java", ".cpp"]:
                content_type = "code"

            # Create document
            document = {
                "id": document_id,
                "title": file_name,
                "content": content,
                "content_type": content_type,
                "metadata": {
                    "file_path": file_path,
                    "file_size": file_size,
                    "file_type": file_type,
                    "extension": ext.lower(),
                },
                "source": "file",
                "url": f"file://{file_path}",
            }

            return document
        except Exception as e:
            print(f"Error processing file {file_path}: {e}")
            return None

    def validate_config(self) -> bool:
        """
        Validate the configuration.

        Returns:
            bool: True if the configuration is valid, False otherwise
        """
        # Check if the base path exists
        if not os.path.exists(self.base_path):
            return False

        return True

    def get_source_type(self) -> str:
        """
        Get the type of the source.

        Returns:
            str: Type of the source
        """
        return "file"
