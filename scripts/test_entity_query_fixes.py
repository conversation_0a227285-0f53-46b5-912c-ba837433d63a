#!/usr/bin/env python3
"""
Test script to validate entity-specific query fixes and UI response improvements.

This script tests:
1. Entity focus accuracy (<PERSON><PERSON><PERSON> vs <PERSON>)
2. Response formatting and structure
3. Chronological ordering
4. Citation cleanliness
5. UI compatibility

Usage:
    cd /Users/<USER>/Desktop/RAGSearch/multi_source_rag
    python ../scripts/test_entity_query_fixes.py
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('/Users/<USER>/Desktop/RAGSearch/multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.rag_service import RAGService
from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.core.utils.query_classifier import classify_query
from apps.core.utils.prompt_templates import _extract_entity_instructions
import re


def test_entity_extraction():
    """Test entity extraction from queries."""
    print("🔍 Testing Entity Extraction...")
    
    test_queries = [
        "list issues reported on Curana",
        "show problems with <PERSON>",
        "what issues were reported by <PERSON>",
        "tell me about Yellowstone bugs"
    ]
    
    for query in test_queries:
        classification = classify_query(query)
        entity_instructions = _extract_entity_instructions(query, classification['type'])
        
        print(f"  Query: '{query}'")
        print(f"  Type: {classification['type']} (confidence: {classification['confidence']:.2f})")
        print(f"  Entity Instructions: {entity_instructions[:100]}...")
        print()


def test_response_quality():
    """Test response quality and entity focus."""
    print("🎯 Testing Response Quality...")
    
    # Get tenant and user
    tenant = Tenant.objects.first()
    user = User.objects.first()
    
    if not tenant or not user:
        print("❌ No tenant or user found. Please ensure test data exists.")
        return
    
    rag_service = RAGService(tenant_slug=tenant.slug, user=user)
    
    test_queries = [
        "list issues reported on Curana",
        "what problems did Amanda report",
        "show me Curana login issues"
    ]
    
    for query in test_queries:
        print(f"  Testing: '{query}'")
        
        try:
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=5,
                min_relevance_score=0.10,
                use_hybrid_search=True,
                use_enhanced_prompts=True
            )
            
            response = search_result.generated_answer
            
            # Check entity focus
            entity_focus = check_entity_focus(query, response)
            
            # Check formatting
            formatting_score = check_formatting(response)
            
            # Check chronological order
            chronological_score = check_chronological_order(response)
            
            # Check citation cleanliness
            citation_score = check_citation_cleanliness(response)
            
            print(f"    ✅ Entity Focus: {entity_focus}")
            print(f"    ✅ Formatting: {formatting_score}")
            print(f"    ✅ Chronological: {chronological_score}")
            print(f"    ✅ Citation Clean: {citation_score}")
            print(f"    📊 Citations: {search_result.citations.count()}")
            print(f"    📝 Length: {len(response)} chars")
            print()
            
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
            print()


def check_entity_focus(query, response):
    """Check if response focuses on the correct entity."""
    # Extract entity from query
    entity_patterns = [
        r'(?:about|on|for|regarding|concerning)\s+([A-Za-z][a-zA-Z]+)',
        r'(?:reported by|mentioned by|from)\s+([A-Za-z][a-zA-Z]+)',
        r'(?:list|show)\s+(?:issues|problems).*?(?:on|about|for)\s+([A-Za-z][a-zA-Z]+)',
    ]
    
    entities = []
    for pattern in entity_patterns:
        matches = re.findall(pattern, query, re.IGNORECASE)
        entities.extend(matches)
    
    if not entities:
        return "No entity detected"
    
    target_entity = entities[0].lower()
    response_lower = response.lower()
    
    # Check if target entity appears in response
    if target_entity in response_lower:
        # Check if response title mentions the entity
        if f"issues related to {target_entity}" in response_lower:
            return "✅ Correct entity focus"
        else:
            return "⚠️ Entity mentioned but not focused"
    else:
        return "❌ Wrong entity focus"


def check_formatting(response):
    """Check response formatting quality."""
    checks = []
    
    # Check for summary section
    if "**Summary:**" in response:
        checks.append("Summary section")
    
    # Check for detailed issues section
    if "**Detailed Issues:**" in response:
        checks.append("Detailed section")
    
    # Check for bullet points
    if "•" in response:
        checks.append("Bullet points")
    
    # Check for bold dates
    if re.search(r'\*\*\w+ \d+, \d+\*\*', response):
        checks.append("Bold dates")
    
    # Check for issue titles
    if re.search(r'\*\*[^*]+\*\*:', response):
        checks.append("Issue titles")
    
    return f"{len(checks)}/5 formatting elements"


def check_chronological_order(response):
    """Check if issues are in chronological order (most recent first)."""
    # Extract dates from response
    date_pattern = r'\*\*(\w+ \d+, \d+)\*\*'
    dates = re.findall(date_pattern, response)
    
    if len(dates) < 2:
        return "Not enough dates to check"
    
    # Simple check: February 26 should come before February 25
    if "February 26" in dates[0] and "February 25" in dates[-1]:
        return "✅ Chronological order"
    else:
        return "⚠️ Check chronological order"


def check_citation_cleanliness(response):
    """Check if response is free of citation noise."""
    # Check for inline citation numbers
    if re.search(r'\[\d+\]', response):
        return "❌ Has inline citations"
    
    # Check for document references
    if re.search(r'\[Document \d+', response):
        return "❌ Has document references"
    
    # Check for chunk IDs
    if re.search(r'Chunk Id:', response):
        return "❌ Has chunk IDs"
    
    return "✅ Clean citations"


def main():
    """Run all tests."""
    print("🚀 Testing Entity-Specific Query Fixes & UI Response Improvements")
    print("=" * 70)
    
    test_entity_extraction()
    test_response_quality()
    
    print("✅ All tests completed!")
    print("\nTo test in browser:")
    print("1. Go to http://127.0.0.1:8000/search/")
    print("2. Search: 'list issues reported on Curana'")
    print("3. Verify entity focus and clean formatting")


if __name__ == "__main__":
    main()
