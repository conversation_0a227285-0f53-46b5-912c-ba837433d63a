#!/usr/bin/env python3
"""
Simple GitHub integration test without ingestion.
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.documents.interfaces.factory import DocumentSourceFactory

def test_github_interface_only():
    """Test only the GitHub interface without ingestion."""
    print("🔧 Testing GitHub Interface Only")
    print("=" * 50)
    
    # GitHub configuration
    config = {
        "token": "****************************************",
        "owner": "Compiify",
        "repo": "Yellowstone",
        "content_types": ["pull_request", "issue"]
    }
    
    try:
        # Initialize interface using factory
        github_interface = DocumentSourceFactory.create_source("github", config)
        print(f"✅ Successfully initialized GitHub interface for {config['owner']}/{config['repo']}")
        
        # Test configuration validation
        if github_interface.validate_config():
            print("✅ Configuration validation passed")
        else:
            print("❌ Configuration validation failed")
            return False
        
        # Test fetching documents
        print("\n📥 Fetching documents...")
        documents = github_interface.fetch_documents(
            days=30,
            max_per_type=3  # Limit for testing
        )
        
        print(f"✅ Fetched {len(documents)} documents")
        
        # Display statistics
        stats = github_interface.get_stats()
        print(f"\n📊 Statistics:")
        print(f"   Total documents: {stats['total_documents']}")
        print(f"   Processing time: {stats['processing_time']:.2f}s")
        print(f"   By content type:")
        for content_type, count in stats['by_content_type'].items():
            print(f"     - {content_type}: {count}")
        
        # Display sample documents
        print(f"\n📄 Sample documents:")
        for i, doc in enumerate(documents[:2]):
            print(f"   {i+1}. {doc['title']} ({doc['metadata']['content_type']})")
            print(f"      ID: {doc['id']}")
            print(f"      Created: {doc.get('created_at', 'N/A')}")
            print(f"      Content preview: {doc['content'][:100]}...")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GitHub interface: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_github_interface_only()
    if success:
        print("🎉 GitHub interface test completed successfully!")
    else:
        print("❌ GitHub interface test failed!")
    sys.exit(0 if success else 1)
