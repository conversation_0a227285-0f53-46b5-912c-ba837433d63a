# Administrator Guide

Complete guide for administering and maintaining the RAGSearch system.

## 🔧 System Administration

### Admin Panel Access
- **URL**: `http://localhost:8000/admin/`
- **Credentials**: Use superuser account created during setup
- **Permissions**: Full system access and configuration

### Core Administrative Tasks

**1. User Management**
- Create and manage user accounts
- Assign tenant permissions
- Monitor user activity
- Reset passwords and manage access

**2. Tenant Management**
- Create new tenants for organizations
- Configure tenant-specific settings
- Monitor tenant resource usage
- Manage data isolation

**3. Data Source Configuration**
- Set up Slack integrations
- Configure GitHub repositories
- Manage API tokens and credentials
- Monitor data ingestion status

## 📊 Data Management

### Data Pipeline Operations

**Complete Pipeline (Recommended)**
```bash
cd multi_source_rag
python ../scripts/data_pipeline.py
```

**Individual Operations**
```bash
# Clean all data (use with caution)
python ../scripts/cleanup_data.py

# Ingest fresh data from configured sources
python ../scripts/ingest_data.py

# Validate data consistency and integrity
python ../scripts/validate_data_consistency.py
```

### Data Source Configuration

**1. Slack Integration**
```bash
# In admin panel, create DocumentSource:
Name: "Team Slack"
Source Type: "slack"
Config: {
  "channels": ["general", "engineering", "product"],
  "include_threads": true,
  "max_messages": 10000
}
Credentials: {
  "token": "xoxb-your-slack-token"
}
```

**2. GitHub Integration**
```bash
# In admin panel, create DocumentSource:
Name: "Main Repository"
Source Type: "github"
Config: {
  "repositories": ["owner/repo-name"],
  "include_issues": true,
  "include_prs": true,
  "include_discussions": true
}
Credentials: {
  "token": "ghp_your-github-token"
}
```

### Monitoring Data Health

**1. Regular Validation**
```bash
# Run weekly data consistency checks
python ../scripts/validate_data_consistency.py

# Check for orphaned records
python manage.py check_data_integrity

# Monitor ingestion logs
tail -f logs/ingestion.log
```

**2. Performance Monitoring**
```bash
# Check database performance
python manage.py dbshell
\d+ documents_documentchunk;
\d+ documents_embeddingmetadata;

# Monitor Qdrant performance
curl http://localhost:6333/cluster
curl http://localhost:6333/collections
```

## 🔍 Search System Management

### Search Quality Optimization

**1. Confidence Score Tuning**
- Monitor average confidence scores
- Adjust thresholds in `apps/search/services/rag_service.py`
- Review low-confidence queries for patterns

**2. Prompt Template Management**
- Update templates in `apps/core/utils/prompt_templates.py`
- Test new templates with validation scripts
- Monitor response quality metrics

**3. Embedding Model Management**
- Ensure consistent embedding models across system
- Monitor embedding dimensions (768d for BGE-base-en-v1.5)
- Validate model performance regularly

### Search Performance

**1. Query Performance**
```bash
# Monitor slow queries
python manage.py shell
from apps.search.models import SearchQuery
slow_queries = SearchQuery.objects.filter(processing_time__gt=10.0)
```

**2. Vector Database Optimization**
```bash
# Check Qdrant collection status
curl http://localhost:6333/collections/tenant_stride_default

# Monitor vector count vs Django records
python ../scripts/validate_data_consistency.py
```

## 🛠️ System Maintenance

### Regular Maintenance Tasks

**Daily**
- Monitor system logs for errors
- Check data ingestion status
- Verify search functionality

**Weekly**
- Run data consistency validation
- Review search quality metrics
- Update data sources if needed

**Monthly**
- Full system backup
- Performance optimization review
- Security updates and patches

### Backup and Recovery

**1. Database Backup**
```bash
# PostgreSQL backup
pg_dump multi_source_rag > backup_$(date +%Y%m%d).sql

# Restore from backup
psql multi_source_rag < backup_20240603.sql
```

**2. Qdrant Backup**
```bash
# Create Qdrant snapshot
curl -X POST http://localhost:6333/collections/tenant_stride_default/snapshots

# List snapshots
curl http://localhost:6333/collections/tenant_stride_default/snapshots
```

**3. Configuration Backup**
```bash
# Backup environment configuration
cp multi_source_rag/.env backup/.env.$(date +%Y%m%d)

# Backup Django settings
tar -czf backup/django_config_$(date +%Y%m%d).tar.gz multi_source_rag/config/
```

### Log Management

**1. Log Locations**
```bash
# Django application logs
logs/django.log
logs/ingestion.log
logs/search.log

# System logs
/var/log/ragsearch/
```

**2. Log Rotation**
```bash
# Configure logrotate
sudo nano /etc/logrotate.d/ragsearch

# Example configuration:
/path/to/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 ragsearch ragsearch
}
```

## 🔐 Security Management

### Access Control

**1. User Permissions**
- Regular users: Search access only
- Staff users: Admin panel access
- Superusers: Full system access

**2. API Security**
- Monitor API usage and rate limits
- Review authentication logs
- Update API keys regularly

**3. Data Security**
- Ensure tenant data isolation
- Monitor cross-tenant access attempts
- Regular security audits

### Security Best Practices

**1. Environment Security**
```bash
# Secure .env file permissions
chmod 600 multi_source_rag/.env

# Regular security updates
poetry update
pip list --outdated
```

**2. Network Security**
- Configure firewall rules
- Use HTTPS in production
- Secure database connections

## 📈 Performance Optimization

### Database Optimization

**1. Index Management**
```sql
-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats WHERE tablename = 'documents_documentchunk';

-- Add custom indexes for performance
CREATE INDEX CONCURRENTLY idx_chunks_tenant_created 
ON documents_documentchunk(tenant_id, created_at);
```

**2. Query Optimization**
```bash
# Enable query logging
echo "log_statement = 'all'" >> postgresql.conf
echo "log_min_duration_statement = 1000" >> postgresql.conf

# Analyze slow queries
tail -f /var/log/postgresql/postgresql.log | grep "duration:"
```

### Vector Database Optimization

**1. Qdrant Configuration**
```yaml
# qdrant-config.yaml
storage:
  storage_path: ./storage
  snapshots_path: ./snapshots
service:
  http_port: 6333
  grpc_port: 6334
  max_request_size_mb: 32
cluster:
  enabled: false
```

**2. Collection Optimization**
```bash
# Monitor collection metrics
curl http://localhost:6333/collections/tenant_stride_default

# Optimize collection if needed
curl -X POST http://localhost:6333/collections/tenant_stride_default/index
```

## 🚨 Troubleshooting

### Common Issues

**1. Data Inconsistency**
```bash
# Symptoms: Citation lookup errors, missing results
# Solution: Run data consistency validation and cleanup
python ../scripts/validate_data_consistency.py
python ../scripts/cleanup_data.py
python ../scripts/ingest_data.py
```

**2. Poor Search Quality**
```bash
# Symptoms: Low confidence scores, irrelevant results
# Solution: Check embedding model consistency
python manage.py shell
from apps.core.utils.embedding_consistency import validate_embedding_consistency
validate_embedding_consistency()
```

**3. Performance Issues**
```bash
# Symptoms: Slow search responses, timeouts
# Solution: Check database and vector store performance
python ../scripts/validate_data_consistency.py
curl http://localhost:6333/cluster
```

### Emergency Procedures

**1. System Recovery**
```bash
# If system is unresponsive
sudo systemctl restart ragsearch
docker restart qdrant

# If data is corrupted
python ../scripts/cleanup_data.py
# Restore from backup
# Re-run ingestion
```

**2. Data Recovery**
```bash
# Restore from database backup
psql multi_source_rag < backup_latest.sql

# Restore Qdrant from snapshot
curl -X PUT http://localhost:6333/collections/tenant_stride_default/snapshots/upload
```

## 📞 Support and Escalation

### Internal Support
- Check logs and error messages
- Run diagnostic scripts
- Review system metrics

### External Support
- Document issue with logs and steps to reproduce
- Include system configuration details
- Provide relevant error messages and stack traces

For technical architecture details, see [Architecture Guide](ARCHITECTURE.md).
For development and customization, see [Development Guide](DEVELOPMENT.md).
