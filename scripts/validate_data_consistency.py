#!/usr/bin/env python3
"""
Production-grade data consistency validation script.
Validates integrity between Django database and Qdrant vector database.
"""

import os
import sys
import django
import requests
from typing import Dict, Any

# Setup Django
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)
os.chdir(project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.conf import settings
from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import RawDocument, DocumentChunk, DocumentSource, EmbeddingMetadata
from apps.search.models import SearchResult, SearchQuery, ResultCitation
from apps.documents.services.ingestion_service import IngestionService

def get_qdrant_info() -> Dict[str, Any]:
    """Get Qdrant cluster information."""
    qdrant_host = getattr(settings, 'QDRANT_HOST', 'localhost')
    qdrant_port = getattr(settings, 'QDRANT_PORT', 6333)
    qdrant_url = f"http://{qdrant_host}:{qdrant_port}"
    
    try:
        # Get cluster info
        response = requests.get(f"{qdrant_url}/cluster")
        cluster_info = response.json() if response.status_code == 200 else {}
        
        # Get collections
        response = requests.get(f"{qdrant_url}/collections")
        collections_info = response.json() if response.status_code == 200 else {}
        
        collections = collections_info.get('result', {}).get('collections', [])
        
        return {
            "status": "connected",
            "url": qdrant_url,
            "cluster_info": cluster_info,
            "collections": collections,
            "collection_count": len(collections)
        }
        
    except requests.exceptions.ConnectionError:
        return {
            "status": "disconnected",
            "url": qdrant_url,
            "error": "Cannot connect to Qdrant"
        }
    except Exception as e:
        return {
            "status": "error",
            "url": qdrant_url,
            "error": str(e)
        }

def validate_django_database() -> Dict[str, Any]:
    """Validate Django database integrity."""
    print("🔍 VALIDATING DJANGO DATABASE")
    print("-" * 40)
    
    # Get counts
    counts = {
        'tenants': Tenant.objects.count(),
        'users': User.objects.count(),
        'document_sources': DocumentSource.objects.count(),
        'raw_documents': RawDocument.objects.count(),
        'document_chunks': DocumentChunk.objects.count(),
        'embedding_metadata': EmbeddingMetadata.objects.count(),
        'search_queries': SearchQuery.objects.count(),
        'search_results': SearchResult.objects.count(),
        'result_citations': ResultCitation.objects.count(),
    }
    
    print("📊 Database counts:")
    for model, count in counts.items():
        print(f"   {model}: {count:,}")
    
    # Check for orphaned records
    orphaned_chunks = DocumentChunk.objects.filter(document__isnull=True).count()
    orphaned_embeddings = EmbeddingMetadata.objects.filter(chunk__isnull=True).count()
    orphaned_citations = ResultCitation.objects.filter(result__isnull=True).count()
    
    orphans = {
        'orphaned_chunks': orphaned_chunks,
        'orphaned_embeddings': orphaned_embeddings,
        'orphaned_citations': orphaned_citations
    }
    
    print("\n🔗 Orphaned records:")
    for orphan_type, count in orphans.items():
        status = "✅" if count == 0 else "❌"
        print(f"   {status} {orphan_type}: {count:,}")
    
    # Check embedding consistency
    total_chunks = counts['document_chunks']
    total_embeddings = counts['embedding_metadata']
    embedding_coverage = (total_embeddings / total_chunks * 100) if total_chunks > 0 else 0
    
    print(f"\n📈 Embedding coverage: {embedding_coverage:.1f}%")
    
    issues = []
    if orphaned_chunks > 0:
        issues.append(f"{orphaned_chunks} orphaned chunks")
    if orphaned_embeddings > 0:
        issues.append(f"{orphaned_embeddings} orphaned embeddings")
    if orphaned_citations > 0:
        issues.append(f"{orphaned_citations} orphaned citations")
    if embedding_coverage < 95:
        issues.append(f"Low embedding coverage: {embedding_coverage:.1f}%")
    
    return {
        "counts": counts,
        "orphans": orphans,
        "embedding_coverage": embedding_coverage,
        "issues": issues,
        "is_healthy": len(issues) == 0
    }

def validate_qdrant_database() -> Dict[str, Any]:
    """Validate Qdrant vector database."""
    print("\n🔍 VALIDATING QDRANT DATABASE")
    print("-" * 40)
    
    qdrant_info = get_qdrant_info()
    
    if qdrant_info["status"] != "connected":
        print(f"❌ Qdrant connection failed: {qdrant_info.get('error', 'Unknown error')}")
        return {
            "status": qdrant_info["status"],
            "error": qdrant_info.get("error"),
            "is_healthy": False
        }
    
    print(f"✅ Connected to Qdrant: {qdrant_info['url']}")
    print(f"📊 Collections found: {qdrant_info['collection_count']}")
    
    collections_detail = []
    total_vectors = 0
    
    for collection in qdrant_info["collections"]:
        collection_name = collection["name"]
        
        try:
            # Get collection info
            qdrant_url = qdrant_info["url"]
            response = requests.get(f"{qdrant_url}/collections/{collection_name}")
            
            if response.status_code == 200:
                collection_info = response.json()
                result = collection_info.get("result", {})

                # Use points_count instead of vectors_count for accurate count
                points_count = result.get("points_count", 0)
                indexed_vectors_count = result.get("indexed_vectors_count", 0)
                total_vectors += points_count

                collections_detail.append({
                    "name": collection_name,
                    "points_count": points_count,
                    "indexed_vectors_count": indexed_vectors_count,
                    "status": "healthy"
                })

                print(f"   ✅ {collection_name}: {points_count:,} points ({indexed_vectors_count:,} indexed)")
            else:
                collections_detail.append({
                    "name": collection_name,
                    "status": "error",
                    "error": f"HTTP {response.status_code}"
                })
                print(f"   ❌ {collection_name}: Error getting info")
                
        except Exception as e:
            collections_detail.append({
                "name": collection_name,
                "status": "error",
                "error": str(e)
            })
            print(f"   ❌ {collection_name}: {e}")
    
    print(f"\n📈 Total vectors: {total_vectors:,}")
    
    return {
        "status": "connected",
        "url": qdrant_info["url"],
        "collection_count": qdrant_info["collection_count"],
        "collections": collections_detail,
        "total_vectors": total_vectors,
        "is_healthy": all(c.get("status") == "healthy" for c in collections_detail)
    }

def validate_vector_references(django_embeddings, qdrant_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate actual vector references between PostgreSQL and Qdrant.

    Args:
        django_embeddings: QuerySet of EmbeddingMetadata objects
        qdrant_info: Qdrant connection info

    Returns:
        Dict with detailed reference validation results
    """
    print("🔍 Performing deep reference validation...")

    # Sample size for validation (adjust based on data size)
    total_embeddings = django_embeddings.count()
    sample_size = min(100, total_embeddings)  # Check up to 100 vectors

    if total_embeddings == 0:
        return {
            "vectors_checked": 0,
            "found_in_qdrant": 0,
            "missing_from_qdrant": 0,
            "orphaned_in_qdrant": 0,
            "reference_accuracy": 1.0,
            "sample_size": 0,
            "total_embeddings": 0
        }

    # Get a representative sample of embeddings
    sample_embeddings = django_embeddings.order_by('?')[:sample_size]

    found_in_qdrant = 0
    missing_from_qdrant = 0
    checked_vector_ids = set()

    # Check each sampled embedding in Qdrant
    for embedding in sample_embeddings:
        vector_id = embedding.vector_id
        checked_vector_ids.add(vector_id)

        # Check if vector exists in Qdrant
        if check_vector_exists_in_qdrant(vector_id, qdrant_info):
            found_in_qdrant += 1
        else:
            missing_from_qdrant += 1

    # Check for orphaned vectors in Qdrant (vectors that don't exist in Django)
    orphaned_in_qdrant = count_orphaned_vectors_in_qdrant(django_embeddings, qdrant_info)

    # Calculate accuracy
    vectors_checked = len(checked_vector_ids)
    reference_accuracy = found_in_qdrant / vectors_checked if vectors_checked > 0 else 0

    return {
        "vectors_checked": vectors_checked,
        "found_in_qdrant": found_in_qdrant,
        "missing_from_qdrant": missing_from_qdrant,
        "orphaned_in_qdrant": orphaned_in_qdrant,
        "reference_accuracy": reference_accuracy,
        "sample_size": sample_size,
        "total_embeddings": total_embeddings
    }

def check_vector_exists_in_qdrant(vector_id: str, qdrant_info: Dict[str, Any]) -> bool:
    """
    Check if a specific vector ID exists in Qdrant.

    Args:
        vector_id: Vector ID to check
        qdrant_info: Qdrant connection info

    Returns:
        True if vector exists, False otherwise
    """
    try:
        qdrant_url = qdrant_info["url"]

        # Get the collection name (assuming tenant-based naming)
        collections = qdrant_info.get("collections", [])
        if not collections:
            return False

        # Check in the first available collection (or iterate through all)
        for collection in collections:
            collection_name = collection["name"]

            # Use Qdrant's retrieve API to check if vector exists
            response = requests.get(
                f"{qdrant_url}/collections/{collection_name}/points/{vector_id}",
                timeout=5
            )

            if response.status_code == 200:
                return True
            elif response.status_code == 404:
                continue  # Vector not found in this collection
            else:
                # Other error, log and continue
                print(f"   ⚠️  Error checking vector {vector_id}: HTTP {response.status_code}")
                continue

        return False

    except Exception as e:
        print(f"   ⚠️  Error checking vector {vector_id}: {e}")
        return False

def count_orphaned_vectors_in_qdrant(django_embeddings, qdrant_info: Dict[str, Any]) -> int:
    """
    Count vectors in Qdrant that don't have corresponding records in Django.

    Args:
        django_embeddings: QuerySet of EmbeddingMetadata objects
        qdrant_info: Qdrant connection info

    Returns:
        Number of orphaned vectors
    """
    try:
        # Get all Django vector IDs
        django_vector_ids = set(django_embeddings.values_list('vector_id', flat=True))

        qdrant_url = qdrant_info["url"]
        collections = qdrant_info.get("collections", [])

        total_orphaned = 0

        for collection in collections:
            collection_name = collection["name"]

            # Get a sample of vectors from Qdrant to check for orphans
            # Note: This is a simplified check. For large collections, you might want to implement pagination
            response = requests.post(
                f"{qdrant_url}/collections/{collection_name}/points/scroll",
                json={
                    "limit": 100,  # Check first 100 vectors
                    "with_payload": False,
                    "with_vector": False
                },
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                points = data.get("result", {}).get("points", [])

                for point in points:
                    point_id = str(point.get("id", ""))
                    if point_id and point_id not in django_vector_ids:
                        total_orphaned += 1
            else:
                print(f"   ⚠️  Error checking collection {collection_name}: HTTP {response.status_code}")

        return total_orphaned

    except Exception as e:
        print(f"   ⚠️  Error counting orphaned vectors: {e}")
        return 0

def validate_cross_database_consistency(qdrant_validation_result: Dict[str, Any] = None) -> Dict[str, Any]:
    """Validate consistency between Django and Qdrant with deep reference validation."""
    print("\n🔍 VALIDATING CROSS-DATABASE CONSISTENCY")
    print("-" * 40)

    # Get Django embedding metadata
    django_embeddings = EmbeddingMetadata.objects.all()
    django_vector_ids = set(django_embeddings.values_list('vector_id', flat=True))
    django_count = len(django_vector_ids)

    print(f"📊 Django embedding metadata: {django_count:,}")

    # Use Qdrant validation result if provided, otherwise get fresh info
    if qdrant_validation_result and qdrant_validation_result.get("status") != "error":
        qdrant_total_vectors = qdrant_validation_result.get("total_vectors", 0)
        qdrant_info = {
            "status": "connected",
            "collections": qdrant_validation_result.get("collections", []),
            "url": "http://localhost:6333"  # Default URL
        }
    else:
        # Get Qdrant info
        qdrant_info = get_qdrant_info()

        if qdrant_info["status"] != "connected":
            return {
                "error": "Cannot connect to Qdrant for consistency check",
                "is_consistent": False
            }

        qdrant_total_vectors = sum(
            c.get("points_count", 0) for c in qdrant_info.get("collections", [])
            if c.get("status") == "healthy"
        )

    print(f"📊 Qdrant total vectors: {qdrant_total_vectors:,}")

    # Deep reference validation
    reference_validation = {
        "vectors_checked": min(100, django_count),
        "found_in_qdrant": 0,  # Will be updated by actual validation
        "missing_from_qdrant": min(100, django_count),
        "orphaned_in_qdrant": 0,
        "reference_accuracy": 0.0,
        "sample_size": min(100, django_count),
        "total_embeddings": django_count
    }

    # Perform actual reference validation if we have vectors to check
    if django_count > 0 and qdrant_total_vectors > 0:
        print("🔍 Performing deep reference validation...")
        reference_validation = validate_vector_references(django_embeddings, qdrant_info)
    elif django_count > 0:
        print("🔍 Skipping reference validation - no vectors in Qdrant")
    else:
        print("🔍 Skipping reference validation - no embeddings in Django")

    # Calculate consistency metrics
    vector_count_diff = abs(django_count - qdrant_total_vectors)
    consistency_ratio = min(django_count, qdrant_total_vectors) / max(django_count, qdrant_total_vectors) if max(django_count, qdrant_total_vectors) > 0 else 1

    print(f"📈 Vector count difference: {vector_count_diff:,}")
    print(f"📈 Consistency ratio: {consistency_ratio:.3f}")

    # Print reference validation results
    print(f"🔗 Reference validation:")
    print(f"   Vectors checked: {reference_validation['vectors_checked']:,}")
    print(f"   Found in Qdrant: {reference_validation['found_in_qdrant']:,}")
    print(f"   Missing from Qdrant: {reference_validation['missing_from_qdrant']:,}")
    print(f"   Orphaned in Qdrant: {reference_validation['orphaned_in_qdrant']:,}")
    print(f"   Reference accuracy: {reference_validation['reference_accuracy']:.1%}")

    # Determine if consistent (stricter criteria with reference validation)
    is_consistent = (
        consistency_ratio >= 0.95 and  # Within 5% of each other
        vector_count_diff <= max(django_count, qdrant_total_vectors) * 0.05 and  # Less than 5% difference
        reference_validation['reference_accuracy'] >= 0.95  # 95% of references must be valid
    )

    status = "✅ Consistent" if is_consistent else "❌ Inconsistent"
    print(f"🎯 Status: {status}")

    return {
        "django_count": django_count,
        "qdrant_count": qdrant_total_vectors,
        "count_difference": vector_count_diff,
        "consistency_ratio": consistency_ratio,
        "reference_validation": reference_validation,
        "is_consistent": is_consistent
    }

def validate_source_specific_consistency(tenant: Tenant) -> Dict[str, Any]:
    """Validate consistency for each document source."""
    print("\n🔍 VALIDATING SOURCE-SPECIFIC CONSISTENCY")
    print("-" * 40)
    
    sources = DocumentSource.objects.filter(tenant=tenant)
    source_results = {}
    
    # Initialize ingestion service for validation
    user = User.objects.first()
    ingestion_service = IngestionService(tenant, user)
    
    for source in sources:
        print(f"\n📚 Source: {source.name}")
        
        # Use the new validation method from our improved ingestion service
        validation_result = ingestion_service.validate_data_consistency(source)
        source_results[source.name] = validation_result
        
        # Print results
        print(f"   Total embeddings: {validation_result['total_embeddings']:,}")
        print(f"   Synced embeddings: {validation_result['synced_embeddings']:,}")
        print(f"   Orphaned embeddings: {validation_result['orphaned_embeddings']:,}")
        print(f"   Consistency score: {validation_result['consistency_score']:.1f}%")
        
        status = "✅ Consistent" if validation_result['is_consistent'] else "❌ Inconsistent"
        print(f"   Status: {status}")
    
    # Overall source consistency
    total_sources = len(source_results)
    consistent_sources = sum(1 for r in source_results.values() if r['is_consistent'])
    overall_consistency = consistent_sources / total_sources if total_sources > 0 else 1
    
    print(f"\n📊 Overall source consistency: {consistent_sources}/{total_sources} ({overall_consistency:.1%})")
    
    return {
        "total_sources": total_sources,
        "consistent_sources": consistent_sources,
        "overall_consistency": overall_consistency,
        "source_results": source_results,
        "is_healthy": overall_consistency >= 0.9
    }

def generate_validation_summary(results: Dict[str, Any]) -> Dict[str, Any]:
    """Generate a comprehensive validation summary."""
    return {
        "django_healthy": results["django"]["is_healthy"],
        "qdrant_healthy": results["qdrant"]["is_healthy"],
        "cross_db_consistent": results["cross_db"]["is_consistent"],
        "sources_healthy": results["sources"]["is_healthy"],
        "overall_healthy": all([
            results["django"]["is_healthy"],
            results["qdrant"]["is_healthy"],
            results["cross_db"]["is_consistent"],
            results["sources"]["is_healthy"]
        ])
    }

def main():
    """Main validation function."""
    print("🔍 PRODUCTION-GRADE DATA CONSISTENCY VALIDATION")
    print("=" * 60)
    
    # Get tenant
    tenant = Tenant.objects.first()
    if not tenant:
        print("❌ No tenant found.")
        return False
    
    print(f"✅ Validating tenant: {tenant.name}")
    
    # Run all validations
    results = {}
    
    # 1. Django database validation
    results["django"] = validate_django_database()
    
    # 2. Qdrant database validation
    results["qdrant"] = validate_qdrant_database()

    # 3. Cross-database consistency
    results["cross_db"] = validate_cross_database_consistency(results["qdrant"])
    
    # 4. Source-specific consistency
    results["sources"] = validate_source_specific_consistency(tenant)

    # Generate summary
    summary = generate_validation_summary(results)

    # Final summary
    print(f"\n🎯 VALIDATION SUMMARY")
    print("=" * 60)
    print(f"   Django database: {'✅ Healthy' if summary['django_healthy'] else '❌ Issues found'}")
    print(f"   Qdrant database: {'✅ Healthy' if summary['qdrant_healthy'] else '❌ Issues found'}")
    print(f"   Cross-DB consistency: {'✅ Consistent' if summary['cross_db_consistent'] else '❌ Inconsistent'}")
    print(f"   Source consistency: {'✅ Healthy' if summary['sources_healthy'] else '❌ Issues found'}")

    print(f"\n🏆 OVERALL STATUS: {'✅ HEALTHY' if summary['overall_healthy'] else '❌ ISSUES DETECTED'}")

    return summary['overall_healthy']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
