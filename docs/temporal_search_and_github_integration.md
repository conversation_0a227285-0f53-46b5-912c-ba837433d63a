# Temporal Search and GitHub Integration

This document describes the enhanced RAG search functionality with temporal ordering and the comprehensive GitHub integration implemented in the Multi-Source RAG system.

## Overview

The system now provides:

1. **Temporal Ordering in RAG Search**: Search results are ordered by relevance first, then by timestamp (descending) to show the most recent relevant documents first.

2. **Comprehensive GitHub Integration**: A consolidated GitHub interface that pulls data from multiple GitHub sources including PRs, issues, wiki pages, releases, and workflows.

3. **Enhanced Multi-Source Search**: Improved search across both Slack and GitHub sources with proper temporal ordering.

## Features Implemented

### 1. Temporal Ordering in RAG Search

#### Changes Made:
- Modified `RAGService._create_citations()` method to sort citations by relevance and timestamp
- Updated search results template to display humanized timestamps
- Enhanced citation ranking to prioritize recent documents among equally relevant results

#### How It Works:
```python
# Primary sort: relevance (descending)
# Secondary sort: timestamp (descending)
citations.sort(key=lambda c: (
    -c.relevance_score,
    -(c.document_chunk.document.created_at.timestamp() if c.document_chunk.document.created_at else 0)
))
```

#### Benefits:
- Users see the most recent relevant information first
- Better context for time-sensitive queries
- Improved user experience with temporal context

### 2. Consolidated GitHub Integration

#### New Components:

**ConsolidatedGitHubInterface** (`apps/documents/interfaces/github/consolidated_github.py`):
- Unified interface for all GitHub data sources
- Modular content type handling
- Production-ready error handling and rate limiting
- Comprehensive data extraction

#### Supported Content Types:
- **Pull Requests**: With reviews, comments, and file changes
- **Issues**: With comments, labels, and reactions
- **Wiki Pages**: Complete wiki content with history
- **Releases**: Release notes and assets
- **Workflows**: GitHub Actions metadata and run history
- **Discussions**: Placeholder for future GraphQL implementation

#### Configuration:
```python
config = {
    "token": "your_github_token",
    "owner": "repository_owner",
    "repo": "repository_name",
    "content_types": ["pull_request", "issue", "wiki", "release", "workflow"]
}
```

### 3. Enhanced Document Models

#### Updated Models:
- Added new source types: `github_consolidated`, `github_wiki`, `github_discussions`
- Added new content types: `github_wiki`, `github_release`, `github_workflow`
- Increased field lengths to accommodate new types

#### Database Changes:
```sql
-- Source types
ALTER TABLE documents_documentsource ALTER COLUMN source_type TYPE VARCHAR(25);

-- Content types  
ALTER TABLE documents_rawdocument ALTER COLUMN content_type TYPE VARCHAR(25);
```

## Usage

### 1. Setting Up GitHub Sources

```python
from apps.documents.models import DocumentSource
from apps.accounts.models import Tenant

# Create GitHub source
source = DocumentSource.objects.create(
    tenant=tenant,
    name="My GitHub Repo",
    source_type="github_consolidated",
    config={
        "token": "ghp_your_token_here",
        "owner": "your_org",
        "repo": "your_repo",
        "content_types": ["pull_request", "issue", "wiki", "release"]
    },
    is_active=True
)
```

### 2. Running Data Ingestion

Use the provided scripts:

```bash
# Test GitHub integration
python scripts/test_github_integration.py

# Ingest from multiple sources
python scripts/ingest_multi_source.py

# Test temporal search
python scripts/test_temporal_search.py
```

### 3. Performing Temporal Search

```python
from apps.search.services.rag_service import RAGService

# Create RAG service
rag_service = RAGService(user=user, tenant_slug=tenant.slug)

# Perform search with temporal ordering
search_result, retrieved_docs = rag_service.search(
    query_text="Recent engineering discussions",
    top_k=10,
    use_hybrid_search=True,
    use_context_aware=True
)

# Results are automatically ordered by relevance + timestamp
```

## API Endpoints

### Search with Temporal Ordering

**Endpoint**: `POST /api/search/`

**Request**:
```json
{
    "query": "Recent product discussions",
    "top_k": 10,
    "use_hybrid_search": true,
    "use_context_aware": true
}
```

**Response**:
```json
{
    "generated_answer": "Based on recent discussions...",
    "citations": [
        {
            "rank": 1,
            "relevance_score": 0.95,
            "document_title": "Product Planning Meeting",
            "created_at": "2024-01-15T10:30:00Z",
            "source_type": "slack"
        }
    ],
    "processing_time": 2.3
}
```

## Configuration

### Environment Variables

```bash
# GitHub Integration
GITHUB_TOKEN=ghp_your_token_here
GITHUB_OWNER=your_organization
GITHUB_REPOS=repo1,repo2,repo3

# Slack Integration  
SLACK_DATA_DIR=../data/
SLACK_CHANNELS=1-productengineering,engineering-issues
```

### GitHub Repositories

The system is configured to work with:
- **Compiify/Yellowstone**: Main application repository
- **Compiify/Yosemite**: Secondary repository

### Slack Channels

Configured channels:
- **1-productengineering** (C065QSSNH8A)
- **engineering-issues** (C07M2CAS79S)
- **engineering-team**

## Testing

### Test Scripts

1. **GitHub Integration Test**: `scripts/test_github_integration.py`
   - Tests consolidated GitHub interface
   - Validates data fetching and processing
   - Checks ingestion service integration

2. **Temporal Search Test**: `scripts/test_temporal_search.py`
   - Tests temporal ordering in search results
   - Analyzes document distribution by time
   - Validates cross-source search functionality

3. **Multi-Source Ingestion**: `scripts/ingest_multi_source.py`
   - Ingests data from both Slack and GitHub
   - Handles error recovery and reporting
   - Provides comprehensive statistics

### Running Tests

```bash
# Run all tests
python scripts/test_github_integration.py
python scripts/test_temporal_search.py
python scripts/ingest_multi_source.py

# Test basic search functionality
python scripts/test_search.py
```

## Troubleshooting

### Common Issues

1. **GitHub API Rate Limiting**:
   - The system includes automatic rate limit handling
   - Waits for rate limit reset when exceeded
   - Logs rate limit hits for monitoring

2. **Missing Documents**:
   - Check source configuration
   - Verify API tokens and permissions
   - Review ingestion logs for errors

3. **Poor Search Results**:
   - Ensure documents are properly indexed
   - Check embedding model consistency
   - Verify temporal ordering is working

### Debugging

Enable debug logging:
```python
import logging
logging.getLogger('apps.search').setLevel(logging.DEBUG)
logging.getLogger('apps.documents').setLevel(logging.DEBUG)
```

## Future Enhancements

1. **GitHub Discussions**: Implement GraphQL API integration
2. **Project Boards**: Add support for GitHub Projects v2
3. **Advanced Temporal Queries**: Add date range filtering
4. **Real-time Updates**: Implement webhook-based updates
5. **Cross-Platform References**: Enhanced linking between Slack and GitHub

## Performance Considerations

- **Batch Processing**: Use appropriate batch sizes for ingestion
- **Rate Limiting**: Respect GitHub API limits
- **Caching**: Implement caching for frequently accessed data
- **Indexing**: Ensure proper database indexing for temporal queries

## Security

- **Token Management**: Store GitHub tokens securely
- **Access Control**: Implement proper tenant isolation
- **Data Privacy**: Ensure sensitive data is properly handled
- **Audit Logging**: Track all data access and modifications
