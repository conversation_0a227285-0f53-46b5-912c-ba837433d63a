# Troubleshooting Guide

Common issues, solutions, and diagnostic procedures for RAGSearch.

## 🚨 Quick Diagnostics

### System Health Check
```bash
# Complete system validation
cd multi_source_rag
python ../scripts/validate_data_consistency.py

# Quick connectivity tests
curl http://localhost:8000/api/health/
curl http://localhost:6333/cluster
psql -h localhost -U your_user -d multi_source_rag -c "SELECT 1;"
```

### Common Error Patterns
```bash
# Check recent logs for errors
tail -100 logs/django.log | grep ERROR
tail -100 logs/ingestion.log | grep ERROR

# Check Django system status
python manage.py check
python manage.py check --database default
```

## 🔧 Installation & Setup Issues

### 1. Database Connection Errors

**Error**: `django.db.utils.OperationalError: could not connect to server`

**Symptoms**:
- Django fails to start
- Migration errors
- Database connection timeouts

**Solutions**:
```bash
# Check PostgreSQL status
pg_ctl status
sudo systemctl status postgresql

# Test connection manually
psql -h localhost -U your_user -d multi_source_rag

# Check .env configuration
cat multi_source_rag/.env | grep DB_

# Common fixes:
# 1. Start PostgreSQL
sudo systemctl start postgresql

# 2. Create database if missing
createdb multi_source_rag

# 3. Check user permissions
psql -c "ALTER USER your_user CREATEDB;"
```

### 2. Qdrant Connection Issues

**Error**: `requests.exceptions.ConnectionError: Cannot connect to Qdrant`

**Symptoms**:
- Vector search failures
- Ingestion errors
- Validation script failures

**Solutions**:
```bash
# Check if Qdrant is running
docker ps | grep qdrant
curl http://localhost:6333/cluster

# Start Qdrant if not running
docker run -d -p 6333:6333 --name qdrant qdrant/qdrant

# Check port availability
netstat -tulpn | grep 6333
lsof -i :6333

# Test connection
curl -X GET http://localhost:6333/collections
```

### 3. Environment Configuration

**Error**: `KeyError: 'GEMINI_API_KEY'`

**Symptoms**:
- LLM initialization failures
- Search functionality broken
- Missing environment variables

**Solutions**:
```bash
# Check .env file location and content
ls -la multi_source_rag/.env
cat multi_source_rag/.env | grep GEMINI

# Verify environment loading
cd multi_source_rag
python manage.py shell
import os
print(os.environ.get('GEMINI_API_KEY'))

# Common fixes:
# 1. Ensure .env is in correct location (multi_source_rag/.env)
# 2. Check for typos in variable names
# 3. Verify API key is valid
curl -H "Content-Type: application/json" \
     -d '{"contents":[{"parts":[{"text":"Hello"}]}]}' \
     "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=YOUR_API_KEY"
```

## 🔍 Search & RAG Issues

### 1. No Search Results

**Error**: Empty search results or "No relevant documents found"

**Symptoms**:
- All queries return empty results
- Search interface shows no data
- Low or zero document counts

**Diagnosis**:
```bash
# Check if data exists
python manage.py shell
from apps.documents.models import DocumentChunk
print(f"Total chunks: {DocumentChunk.objects.count()}")

from apps.documents.models import EmbeddingMetadata
print(f"Total embeddings: {EmbeddingMetadata.objects.count()}")

# Check Qdrant collections
curl http://localhost:6333/collections
```

**Solutions**:
```bash
# 1. Run data ingestion
python ../scripts/ingest_data.py

# 2. If data exists but search fails, check embedding consistency
python ../scripts/validate_data_consistency.py

# 3. Clear and re-ingest data
python ../scripts/cleanup_data.py
python ../scripts/ingest_data.py
```

### 2. Poor Search Quality

**Error**: Low confidence scores, irrelevant results

**Symptoms**:
- Confidence scores consistently below 0.7
- Results don't match query intent
- Missing obvious relevant documents

**Diagnosis**:
```bash
# Check embedding model consistency
python manage.py shell
from apps.core.utils.embedding_consistency import get_embedding_model_info
print(get_embedding_model_info())

# Check recent search results
from apps.search.models import SearchResult
recent = SearchResult.objects.order_by('-created_at')[:5]
for r in recent:
    print(f"Query: {r.query_text}")
    print(f"Confidence: {r.llm_confidence_score}")
    print(f"Citations: {r.citations.count()}")
```

**Solutions**:
```bash
# 1. Verify embedding model consistency
python manage.py shell
from apps.core.utils.embedding_consistency import validate_embedding_consistency
validate_embedding_consistency()

# 2. Check prompt templates
# Review and update templates in apps/core/utils/prompt_templates.py

# 3. Re-ingest with fresh embeddings
python ../scripts/cleanup_data.py
python ../scripts/ingest_data.py
```

### 3. Citation Lookup Errors

**Error**: `Could not find chunk for node [ID] using any lookup method`

**Symptoms**:
- Search works but no citations appear
- Console errors about missing chunks
- Citation count is 0

**Diagnosis**:
```bash
# Check for data consistency issues
python ../scripts/validate_data_consistency.py

# Check embedding metadata
python manage.py shell
from apps.documents.models import EmbeddingMetadata
orphaned = EmbeddingMetadata.objects.filter(chunk__isnull=True)
print(f"Orphaned embeddings: {orphaned.count()}")
```

**Solutions**:
```bash
# This is a data consistency issue - run complete cleanup and re-ingestion
python ../scripts/cleanup_data.py
python ../scripts/ingest_data.py
python ../scripts/validate_data_consistency.py
```

## 📊 Data Pipeline Issues

### 1. Ingestion Failures

**Error**: Documents fail to process during ingestion

**Symptoms**:
- Ingestion script reports failures
- Processing jobs show error status
- Partial data ingestion

**Diagnosis**:
```bash
# Check processing job logs
python manage.py shell
from apps.documents.models import DocumentProcessingJob
failed_jobs = DocumentProcessingJob.objects.filter(status='failed')
for job in failed_jobs:
    print(f"Job {job.id}: {job.error_message}")

# Check source configuration
from apps.documents.models import DocumentSource
sources = DocumentSource.objects.all()
for source in sources:
    print(f"Source: {source.name}, Type: {source.source_type}")
    print(f"Config: {source.config}")
```

**Solutions**:
```bash
# 1. Check API credentials and permissions
# For Slack: Verify token has required scopes
# For GitHub: Verify token has repo access

# 2. Test source connectivity
python manage.py shell
from apps.documents.services.ingestion_service import IngestionService
# Test individual sources

# 3. Check for rate limiting
# Review API documentation for rate limits
# Implement delays if necessary
```

### 2. Memory Issues During Ingestion

**Error**: `MemoryError` or system becomes unresponsive

**Symptoms**:
- Ingestion process crashes
- System runs out of memory
- Docker containers restart

**Solutions**:
```bash
# 1. Reduce batch size
export INGESTION_BATCH_SIZE=25

# 2. Monitor memory usage
htop
docker stats

# 3. Increase system memory or use swap
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 4. Process sources individually
python ../scripts/ingest_data.py
# Select option 2 (specific source)
```

### 3. Validation Failures

**Error**: Data consistency validation reports issues

**Symptoms**:
- Validation script shows inconsistencies
- Vector count mismatches
- Orphaned records detected

**Solutions**:
```bash
# 1. Review validation report details
python ../scripts/validate_data_consistency.py > validation_report.txt
cat validation_report.txt

# 2. Clean and re-ingest if consistency is poor
python ../scripts/cleanup_data.py
python ../scripts/ingest_data.py

# 3. For minor inconsistencies, try targeted fixes
python manage.py shell
from apps.documents.models import EmbeddingMetadata
# Remove orphaned embeddings
EmbeddingMetadata.objects.filter(chunk__isnull=True).delete()
```

## 🌐 Web Interface Issues

### 1. Search Interface Not Loading

**Error**: 404 or 500 errors on search page

**Symptoms**:
- Search page returns errors
- Static files not loading
- JavaScript errors in browser console

**Solutions**:
```bash
# 1. Check Django server status
python manage.py runserver
# Look for error messages

# 2. Collect static files
python manage.py collectstatic --noinput

# 3. Check URL configuration
python manage.py shell
from django.urls import reverse
print(reverse('search:index'))

# 4. Check for JavaScript errors
# Open browser developer tools
# Look for console errors
```

### 2. Authentication Issues

**Error**: Login failures or permission denied

**Symptoms**:
- Cannot access admin panel
- User authentication fails
- Permission denied errors

**Solutions**:
```bash
# 1. Create/reset superuser
python manage.py createsuperuser

# 2. Check user permissions
python manage.py shell
from django.contrib.auth.models import User
user = User.objects.get(username='your_username')
print(f"Is staff: {user.is_staff}")
print(f"Is superuser: {user.is_superuser}")

# 3. Reset password
python manage.py changepassword your_username
```

## 🔧 Performance Issues

### 1. Slow Search Responses

**Error**: Search takes too long to respond

**Symptoms**:
- Search timeouts
- Poor user experience
- High server load

**Diagnosis**:
```bash
# Check database performance
python manage.py shell
from django.db import connection
print(connection.queries)

# Check Qdrant performance
curl http://localhost:6333/cluster

# Monitor system resources
htop
iostat -x 1
```

**Solutions**:
```bash
# 1. Add database indexes
python manage.py dbshell
CREATE INDEX CONCURRENTLY idx_chunks_tenant_created 
ON documents_documentchunk(tenant_id, created_at);

# 2. Optimize Qdrant configuration
# Review collection settings
curl http://localhost:6333/collections/tenant_stride_default

# 3. Reduce search complexity
# Lower top_k values in search queries
# Optimize prompt templates
```

### 2. High Memory Usage

**Error**: System runs out of memory

**Solutions**:
```bash
# 1. Monitor memory usage
free -h
ps aux --sort=-%mem | head

# 2. Optimize Django settings
# Add to settings.py:
DATABASES['default']['CONN_MAX_AGE'] = 60
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}

# 3. Restart services periodically
sudo systemctl restart ragsearch
docker restart qdrant
```

## 🆘 Emergency Recovery

### Complete System Reset

**When to use**: System is completely broken or corrupted

```bash
# 1. Stop all services
sudo systemctl stop ragsearch
docker stop qdrant

# 2. Backup current state (optional)
pg_dump multi_source_rag > emergency_backup.sql

# 3. Complete cleanup
python ../scripts/cleanup_data.py

# 4. Restart services
docker start qdrant
sudo systemctl start ragsearch

# 5. Re-initialize
python manage.py migrate
python ../scripts/ingest_data.py
python ../scripts/validate_data_consistency.py
```

### Data Recovery from Backup

```bash
# 1. Restore database
psql multi_source_rag < backup_file.sql

# 2. Restore Qdrant (if backup available)
curl -X PUT http://localhost:6333/collections/tenant_stride_default/snapshots/upload
# Upload snapshot file

# 3. Validate restoration
python ../scripts/validate_data_consistency.py
```

## 📞 Getting Help

### Information to Collect

When reporting issues, include:

1. **Error Messages**: Complete error text and stack traces
2. **System Information**: OS, Python version, dependency versions
3. **Configuration**: Relevant .env settings (without secrets)
4. **Steps to Reproduce**: Exact steps that cause the issue
5. **Logs**: Relevant log entries from around the time of the issue

### Log Collection

```bash
# Collect system information
python --version
poetry --version
docker --version
psql --version

# Collect recent logs
tail -100 logs/django.log > issue_logs.txt
tail -100 logs/ingestion.log >> issue_logs.txt
docker logs qdrant --tail 100 >> issue_logs.txt

# Collect configuration (remove secrets first)
cp multi_source_rag/.env issue_config.env
# Edit issue_config.env to remove API keys and passwords
```

### Support Channels

1. **Documentation**: Check all guides in docs/ folder
2. **Validation Scripts**: Run diagnostic scripts first
3. **System Logs**: Review logs for specific error messages
4. **Community**: Search for similar issues in project documentation

For architecture details, see [Architecture Guide](ARCHITECTURE.md).
For administrative procedures, see [Admin Guide](ADMIN_GUIDE.md).
