#!/usr/bin/env python3
"""
Master data pipeline script.
Orchestrates cleanup, ingestion, and validation in the correct order.
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def run_script(script_name: str, description: str) -> bool:
    """Run a script and return success status."""
    print(f"\n🚀 {description}")
    print("=" * 60)
    
    script_path = os.path.join(os.path.dirname(__file__), script_name)
    
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=False, 
                              text=True)
        
        success = result.returncode == 0
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"\n{status}: {description}")
        
        return success
        
    except Exception as e:
        print(f"❌ ERROR running {script_name}: {e}")
        return False

def main():
    """Main pipeline orchestration."""
    print("🔄 COMPLETE DATA PIPELINE")
    print("=" * 60)
    print("This will run the complete data pipeline:")
    print("1. 🧹 Clean up existing data")
    print("2. 📥 Ingest fresh data")
    print("3. 🔍 Validate data consistency")
    print()
    
    # Get user confirmation
    choice = input("Do you want to run the complete pipeline? (y/N): ").strip().lower()
    
    if choice not in ['y', 'yes']:
        print("👋 Pipeline cancelled.")
        return True
    
    start_time = time.time()
    
    # Step 1: Cleanup
    print(f"\n📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    cleanup_success = run_script("cleanup_data.py", "STEP 1: DATA CLEANUP")
    
    if not cleanup_success:
        print("\n❌ Pipeline failed at cleanup step.")
        return False
    
    # Brief pause
    time.sleep(2)
    
    # Step 2: Ingestion
    ingest_success = run_script("ingest_data.py", "STEP 2: DATA INGESTION")
    
    if not ingest_success:
        print("\n❌ Pipeline failed at ingestion step.")
        return False
    
    # Brief pause
    time.sleep(2)
    
    # Step 3: Validation
    validate_success = run_script("validate_data_consistency.py", "STEP 3: DATA VALIDATION")
    
    # Calculate total time
    total_time = time.time() - start_time
    
    # Final summary
    print(f"\n🏁 PIPELINE COMPLETE")
    print("=" * 60)
    print(f"   Cleanup: {'✅ Success' if cleanup_success else '❌ Failed'}")
    print(f"   Ingestion: {'✅ Success' if ingest_success else '❌ Failed'}")
    print(f"   Validation: {'✅ Success' if validate_success else '❌ Failed'}")
    print(f"   Total time: {total_time:.2f} seconds")
    print(f"   Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    overall_success = cleanup_success and ingest_success and validate_success
    
    if overall_success:
        print(f"\n🎉 PIPELINE SUCCESSFUL!")
        print("Your RAG system is ready with fresh, consistent data.")
    else:
        print(f"\n⚠️ PIPELINE COMPLETED WITH ISSUES")
        print("Check the logs above for details.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
