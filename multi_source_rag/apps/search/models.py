from apps.core.models import TenantAwareModel
from apps.documents.models import DocumentChunk
from django.contrib.auth.models import User
from django.db import models


class UserSession(TenantAwareModel):
    """
    Tracks user interaction sessions, allowing for contextual conversations and history-aware responses.
    """

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="sessions")
    started_at = models.DateTimeField(auto_now_add=True)
    context_metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"Session {self.id} - {self.user.username}"


class SearchQuery(TenantAwareModel):
    """
    Records user queries within a tenant's environment, linking to user sessions for conversation context preservation.
    """

    query_text = models.TextField()
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="search_queries"
    )
    session = models.ForeignKey(
        UserSession,
        on_delete=models.CASCADE,
        related_name="queries",
        null=True,
        blank=True,
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    search_params = models.J<PERSON><PERSON>ield(default=dict, blank=True)

    def __str__(self):
        return f"{self.query_text[:50]}..."


class SearchResult(models.Model):
    """
    Stores generated answers and metadata about retrieval quality, with relevance scoring for analytical purposes.
    """

    search_query = models.ForeignKey(
        SearchQuery, on_delete=models.CASCADE, related_name="results"
    )
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="search_results"
    )
    generated_answer = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    retriever_score_avg = models.FloatField(default=0.0)
    llm_confidence_score = models.FloatField(default=0.0)
    metadata = models.JSONField(default=dict, blank=True)

    # Many-to-many relationship with DocumentChunk through ResultCitation
    cited_chunks = models.ManyToManyField(
        DocumentChunk, through="ResultCitation", related_name="cited_in_results"
    )

    class Meta:
        ordering = ["-timestamp"]

    def __str__(self):
        return f"Result for {self.search_query}"


class ResultCitation(models.Model):
    """
    Links search results to the document chunks that were used to generate the answer.
    """

    result = models.ForeignKey(
        SearchResult, on_delete=models.CASCADE, related_name="citations"
    )
    document_chunk = models.ForeignKey(
        DocumentChunk, on_delete=models.CASCADE, related_name="result_citations"
    )
    relevance_score = models.FloatField(default=0.0)
    rank = models.IntegerField(default=0)

    class Meta:
        ordering = ["result", "rank"]
        unique_together = ("result", "document_chunk")

    def __str__(self):
        return f"Citation {self.rank} for {self.result}"


class Feedback(models.Model):
    """
    Captures user feedback on search results, enabling quality improvement and performance tracking.
    """

    result = models.ForeignKey(
        SearchResult, on_delete=models.CASCADE, related_name="feedback"
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="feedback")
    is_helpful = models.BooleanField()
    comment = models.TextField(blank=True, null=True)
    submitted_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Feedback on {self.result} by {self.user.username}"


class Conversation(TenantAwareModel):
    """
    Model to store conversations.
    """

    title = models.CharField(max_length=255, blank=True, null=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="conversations"
    )
    is_active = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ["-updated_at"]

    def __str__(self):
        return self.title or f"Conversation {self.id}"

    def save(self, *args, **kwargs):
        if not self.title and self.messages.exists():
            # Set the title to the first user message
            first_message = self.messages.filter(is_user=True).first()
            if first_message:
                self.title = first_message.content[:50]
        super().save(*args, **kwargs)


class Message(models.Model):
    """
    Model to store messages in a conversation.
    """

    conversation = models.ForeignKey(
        Conversation, on_delete=models.CASCADE, related_name="messages"
    )
    content = models.TextField()
    is_user = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    search_query = models.ForeignKey(
        SearchQuery,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="messages",
    )
    search_result = models.ForeignKey(
        SearchResult,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="messages",
    )

    class Meta:
        ordering = ["conversation", "created_at"]

    def __str__(self):
        return f"{'User' if self.is_user else 'Assistant'}: {self.content[:50]}..."
