#!/usr/bin/env python3
"""
Test script to validate RAG service fixes and improvements.

This script tests:
1. Service initialization with new parameters
2. Input validation
3. Error handling improvements
4. Backward compatibility
5. Feature functionality
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "multi_source_rag"))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

import logging
from django.contrib.auth.models import User
from apps.search.services.rag_service import RAGService

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_service_initialization():
    """Test service initialization with new parameters."""
    print("\n🔧 Testing Service Initialization...")
    
    try:
        # Get or create test user
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # Test default initialization
        service1 = RAGService(user=user, tenant_slug='default')
        print(f"✅ Default initialization successful")
        print(f"   - Default collection intent: {service1.default_collection_intent}")
        print(f"   - Fallback mode: {service1._use_fallback_mode}")
        
        # Test custom collection intent
        service2 = RAGService(user=user, tenant_slug='default', default_collection_intent='custom')
        print(f"✅ Custom collection intent initialization successful")
        print(f"   - Custom collection intent: {service2.default_collection_intent}")
        
        return True
        
    except Exception as e:
        print(f"❌ Service initialization failed: {str(e)}")
        return False

def test_input_validation():
    """Test input validation in search method."""
    print("\n🔍 Testing Input Validation...")
    
    try:
        user, _ = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        service = RAGService(user=user, tenant_slug='default')
        
        # Test empty query
        try:
            service.search("")
            print("❌ Empty query validation failed - should have raised ValueError")
            return False
        except ValueError as e:
            print(f"✅ Empty query validation: {str(e)}")
        
        # Test invalid top_k
        try:
            service.search("test query", top_k=0)
            print("❌ Invalid top_k validation failed - should have raised ValueError")
            return False
        except ValueError as e:
            print(f"✅ Invalid top_k validation: {str(e)}")
        
        # Test invalid relevance score
        try:
            service.search("test query", min_relevance_score=1.5)
            print("❌ Invalid relevance score validation failed - should have raised ValueError")
            return False
        except ValueError as e:
            print(f"✅ Invalid relevance score validation: {str(e)}")
        
        print("✅ All input validation tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Input validation test failed: {str(e)}")
        return False

def test_backward_compatibility():
    """Test that existing code still works with the changes."""
    print("\n🔄 Testing Backward Compatibility...")
    
    try:
        user, _ = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # Test old-style initialization (should still work)
        service = RAGService(user=user, tenant_slug='default')
        
        # Test that all original methods exist
        assert hasattr(service, 'search'), "search method missing"
        assert hasattr(service, 'get_stats'), "get_stats method missing"
        assert hasattr(service, 'get_system_stats'), "get_system_stats method missing"
        
        # Test method signatures are compatible
        stats = service.get_stats()
        assert isinstance(stats, dict), "get_stats should return dict"
        
        print("✅ Backward compatibility maintained")
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {str(e)}")
        return False

def test_error_handling():
    """Test improved error handling."""
    print("\n⚠️ Testing Error Handling...")
    
    try:
        user, _ = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # Test invalid tenant
        try:
            RAGService(user=user, tenant_slug='nonexistent_tenant')
            print("❌ Invalid tenant handling failed - should have raised ValueError")
            return False
        except ValueError as e:
            print(f"✅ Invalid tenant handling: {str(e)}")
        
        print("✅ Error handling tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {str(e)}")
        return False

def test_feature_functionality():
    """Test that core features still work."""
    print("\n🚀 Testing Feature Functionality...")
    
    try:
        user, _ = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        service = RAGService(user=user, tenant_slug='default')
        
        # Test stats functionality
        stats = service.get_stats()
        expected_keys = [
            'queries_processed', 'total_processing_time', 'average_processing_time',
            'query_expansions_used', 'multi_step_queries', 'hybrid_searches'
        ]
        
        for key in expected_keys:
            assert key in stats, f"Missing stat key: {key}"
        
        print("✅ Stats functionality working")
        
        # Test that service has all required components
        assert hasattr(service, 'query_engines'), "query_engines missing"
        assert hasattr(service, 'router_engine'), "router_engine missing"
        assert hasattr(service, 'citation_engine'), "citation_engine missing"
        
        print("✅ Service components initialized")
        
        print("✅ All feature functionality tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Feature functionality test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🧪 RAG Service Fixes Validation")
    print("=" * 50)
    
    tests = [
        ("Service Initialization", test_service_initialization),
        ("Input Validation", test_input_validation),
        ("Backward Compatibility", test_backward_compatibility),
        ("Error Handling", test_error_handling),
        ("Feature Functionality", test_feature_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! RAG service fixes are working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
