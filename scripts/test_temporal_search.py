#!/usr/bin/env python3
"""
Test script for temporal ordering in RAG search.

This script tests the enhanced RAG search functionality with temporal ordering
across Slack and GitHub sources.
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant, User
from apps.documents.models import DocumentSource, RawDocument

def test_temporal_search():
    """Test RAG search with temporal ordering."""
    print("🔍 Testing Temporal RAG Search")
    print("=" * 50)
    
    try:
        # Get tenant and user
        tenant = Tenant.objects.first()
        user = User.objects.first()
        
        if not tenant or not user:
            print("❌ No tenant or user found. Please create them first.")
            return
        
        print(f"✅ Using tenant: {tenant.name}")
        print(f"✅ Using user: {user.email}")
        
        # Create RAG service
        rag_service = RAGService(user=user, tenant_slug=tenant.slug)
        print("✅ RAG service initialized")
        
        # Test queries that should return time-ordered results
        test_queries = [
            "What did Kapil say about product discussions?",
            "Recent engineering issues and solutions",
            "Latest pull requests and code reviews",
            "Product engineering team updates"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Test Query {i}: {query}")
            print("-" * 40)
            
            # Perform search
            search_result, retrieved_docs = rag_service.search(
                query_text=query,
                top_k=10,
                use_hybrid_search=True,
                use_context_aware=True
            )
            
            print(f"✅ Search completed")
            print(f"📄 Generated answer: {search_result.generated_answer[:200]}...")
            print(f"📚 Retrieved {len(retrieved_docs)} documents")
            
            # Get citations and check temporal ordering
            citations = search_result.citations.select_related(
                'document_chunk__document__source'
            ).order_by('rank')
            
            print(f"🔗 Citations ({len(citations)}):")
            for j, citation in enumerate(citations[:5], 1):
                doc = citation.document_chunk.document
                source_type = doc.source.source_type
                created_at = doc.created_at.strftime("%Y-%m-%d %H:%M") if doc.created_at else "N/A"
                
                print(f"   {j}. [{source_type}] {doc.title[:50]}...")
                print(f"      Created: {created_at}")
                print(f"      Relevance: {citation.relevance_score:.3f}")
                print(f"      Rank: {citation.rank}")
                print()
            
            # Analyze temporal distribution
            if citations:
                dates = [c.document_chunk.document.created_at for c in citations if c.document_chunk.document.created_at]
                if dates:
                    dates.sort(reverse=True)  # Most recent first
                    print(f"📅 Temporal distribution:")
                    print(f"   Most recent: {dates[0].strftime('%Y-%m-%d %H:%M')}")
                    print(f"   Oldest: {dates[-1].strftime('%Y-%m-%d %H:%M')}")
                    
                    # Check if results are properly ordered by relevance + time
                    relevance_scores = [c.relevance_score for c in citations]
                    print(f"🎯 Relevance scores: {[f'{s:.3f}' for s in relevance_scores[:5]]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in temporal search test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_document_distribution():
    """Analyze the distribution of documents by source and time."""
    print("\n📊 Document Distribution Analysis")
    print("=" * 50)
    
    try:
        # Get document counts by source type
        sources = DocumentSource.objects.all()
        
        print("📁 Sources:")
        for source in sources:
            doc_count = RawDocument.objects.filter(source=source).count()
            print(f"   {source.name} ({source.source_type}): {doc_count} documents")
        
        # Get recent documents
        recent_docs = RawDocument.objects.filter(
            created_at__isnull=False
        ).order_by('-created_at')[:10]
        
        print(f"\n📅 Recent Documents (last 10):")
        for i, doc in enumerate(recent_docs, 1):
            created_at = doc.created_at.strftime("%Y-%m-%d %H:%M") if doc.created_at else "N/A"
            source_type = doc.source.source_type
            print(f"   {i}. [{source_type}] {doc.title[:50]}... ({created_at})")
        
        # Get document count by source type
        from django.db.models import Count
        source_stats = RawDocument.objects.values(
            'source__source_type'
        ).annotate(
            count=Count('id')
        ).order_by('-count')
        
        print(f"\n📈 Documents by Source Type:")
        for stat in source_stats:
            source_type = stat['source__source_type']
            count = stat['count']
            print(f"   {source_type}: {count} documents")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in document analysis: {str(e)}")
        return False

def test_cross_source_search():
    """Test search across multiple sources."""
    print("\n🔄 Cross-Source Search Test")
    print("=" * 50)
    
    try:
        # Get tenant and user
        tenant = Tenant.objects.first()
        user = User.objects.first()
        
        # Create RAG service
        rag_service = RAGService(user=user, tenant_slug=tenant.slug)
        
        # Test query that should find results from both Slack and GitHub
        query = "engineering team discussions and code reviews"
        
        print(f"🔍 Query: {query}")
        
        # Perform search
        search_result, retrieved_docs = rag_service.search(
            query_text=query,
            top_k=15,
            use_hybrid_search=True
        )
        
        # Analyze source distribution in results
        citations = search_result.citations.select_related(
            'document_chunk__document__source'
        ).order_by('rank')
        
        source_distribution = {}
        for citation in citations:
            source_type = citation.document_chunk.document.source.source_type
            source_distribution[source_type] = source_distribution.get(source_type, 0) + 1
        
        print(f"📊 Results by source type:")
        for source_type, count in source_distribution.items():
            print(f"   {source_type}: {count} results")
        
        print(f"✅ Cross-source search completed with {len(citations)} total results")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in cross-source search test: {str(e)}")
        return False

def main():
    """Main test function."""
    print("🚀 Temporal RAG Search Test Suite")
    print("=" * 60)
    
    # Test 1: Document distribution analysis
    analyze_success = analyze_document_distribution()
    
    # Test 2: Temporal search functionality
    search_success = test_temporal_search()
    
    # Test 3: Cross-source search
    cross_source_success = test_cross_source_search()
    
    print(f"\n🎉 Test Results Summary:")
    print(f"   Document Analysis: {'✅ PASS' if analyze_success else '❌ FAIL'}")
    print(f"   Temporal Search: {'✅ PASS' if search_success else '❌ FAIL'}")
    print(f"   Cross-Source Search: {'✅ PASS' if cross_source_success else '❌ FAIL'}")
    
    if all([analyze_success, search_success, cross_source_success]):
        print(f"\n🎊 All tests passed! Temporal ordering is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
